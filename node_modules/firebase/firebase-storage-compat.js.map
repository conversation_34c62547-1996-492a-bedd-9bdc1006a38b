{"version": 3, "file": "firebase-storage-compat.js", "sources": ["../util/src/crypt.ts", "../util/src/url.ts", "../util/src/emulator.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../storage/src/implementation/constants.ts", "../storage/src/implementation/error.ts", "../storage/src/implementation/connection.ts", "../storage-compat/src/index.ts", "../storage/src/implementation/location.ts", "../storage/src/implementation/failrequest.ts", "../storage/src/implementation/type.ts", "../storage/src/implementation/url.ts", "../storage/src/implementation/utils.ts", "../storage/src/implementation/request.ts", "../storage/src/implementation/backoff.ts", "../storage/src/implementation/fs.ts", "../storage/src/platform/browser/base64.ts", "../storage/src/implementation/string.ts", "../storage/src/implementation/blob.ts", "../storage/src/implementation/json.ts", "../storage/src/implementation/path.ts", "../storage/src/implementation/metadata.ts", "../storage/src/implementation/list.ts", "../storage/src/implementation/requestinfo.ts", "../storage/src/implementation/requests.ts", "../storage/src/implementation/taskenums.ts", "../storage/src/implementation/observer.ts", "../storage/src/implementation/async.ts", "../storage/src/platform/browser/connection.ts", "../storage/src/task.ts", "../storage/src/reference.ts", "../storage/src/service.ts", "../storage/src/api.ts", "../storage/src/index.ts", "../storage/src/constants.ts", "../storage-compat/src/tasksnapshot.ts", "../storage-compat/src/task.ts", "../storage-compat/src/list.ts", "../storage-compat/src/reference.ts", "../storage-compat/src/service.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nexport function isCloudWorkstation(host: string): boolean {\n  return host.endsWith('.cloudworkstations.dev');\n}\n\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nexport async function pingServer(endpoint: string): Promise<boolean> {\n  const result = await fetch(endpoint, {\n    credentials: 'include'\n  });\n  return result.ok;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64urlEncodeWithoutPadding } from './crypt';\nimport { isCloudWorkstation } from './url';\n\n// Firebase Auth tokens contain snake_case claims following the JWT standard / convention.\n/* eslint-disable camelcase */\n\nexport type FirebaseSignInProvider =\n  | 'custom'\n  | 'email'\n  | 'password'\n  | 'phone'\n  | 'anonymous'\n  | 'google.com'\n  | 'facebook.com'\n  | 'github.com'\n  | 'twitter.com'\n  | 'microsoft.com'\n  | 'apple.com';\n\ninterface FirebaseIdToken {\n  // Always set to https://securetoken.google.com/PROJECT_ID\n  iss: string;\n\n  // Always set to PROJECT_ID\n  aud: string;\n\n  // The user's unique ID\n  sub: string;\n\n  // The token issue time, in seconds since epoch\n  iat: number;\n\n  // The token expiry time, normally 'iat' + 3600\n  exp: number;\n\n  // The user's unique ID. Must be equal to 'sub'\n  user_id: string;\n\n  // The time the user authenticated, normally 'iat'\n  auth_time: number;\n\n  // The sign in provider, only set when the provider is 'anonymous'\n  provider_id?: 'anonymous';\n\n  // The user's primary email\n  email?: string;\n\n  // The user's email verification status\n  email_verified?: boolean;\n\n  // The user's primary phone number\n  phone_number?: string;\n\n  // The user's display name\n  name?: string;\n\n  // The user's profile photo URL\n  picture?: string;\n\n  // Information on all identities linked to this user\n  firebase: {\n    // The primary sign-in provider\n    sign_in_provider: FirebaseSignInProvider;\n\n    // A map of providers to the user's list of unique identifiers from\n    // each provider\n    identities?: { [provider in FirebaseSignInProvider]?: string[] };\n  };\n\n  // Custom claims set by the developer\n  [claim: string]: unknown;\n\n  uid?: never; // Try to catch a common mistake of \"uid\" (should be \"sub\" instead).\n}\n\nexport type EmulatorMockTokenOptions = ({ user_id: string } | { sub: string }) &\n  Partial<FirebaseIdToken>;\n\nexport function createMockUserToken(\n  token: EmulatorMockTokenOptions,\n  projectId?: string\n): string {\n  if (token.uid) {\n    throw new Error(\n      'The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.'\n    );\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n\n  const payload: FirebaseIdToken = {\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    },\n\n    // Override with user options\n    ...token\n  };\n\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [\n    base64urlEncodeWithoutPadding(JSON.stringify(header)),\n    base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n    signature\n  ].join('.');\n}\n\ninterface EmulatorStatusMap {\n  [name: string]: boolean;\n}\nconst emulatorStatus: EmulatorStatusMap = {};\n\ninterface EmulatorSummary {\n  prod: string[];\n  emulator: string[];\n}\n\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary(): EmulatorSummary {\n  const summary: EmulatorSummary = {\n    prod: [],\n    emulator: []\n  };\n  for (const key of Object.keys(emulatorStatus)) {\n    if (emulatorStatus[key]) {\n      summary.emulator.push(key);\n    } else {\n      summary.prod.push(key);\n    }\n  }\n  return summary;\n}\n\nfunction getOrCreateEl(id: string): { created: boolean; element: HTMLElement } {\n  let parentDiv = document.getElementById(id);\n  let created = false;\n  if (!parentDiv) {\n    parentDiv = document.createElement('div');\n    parentDiv.setAttribute('id', id);\n    created = true;\n  }\n  return { created, element: parentDiv };\n}\n\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nexport function updateEmulatorBanner(\n  name: string,\n  isRunningEmulator: boolean\n): void {\n  if (\n    typeof window === 'undefined' ||\n    typeof document === 'undefined' ||\n    !isCloudWorkstation(window.location.host) ||\n    emulatorStatus[name] === isRunningEmulator ||\n    emulatorStatus[name] || // If already set to use emulator, can't go back to prod.\n    previouslyDismissed\n  ) {\n    return;\n  }\n\n  emulatorStatus[name] = isRunningEmulator;\n\n  function prefixedId(id: string): string {\n    return `__firebase__banner__${id}`;\n  }\n  const bannerId = '__firebase__banner';\n  const summary = getEmulatorSummary();\n  const showError = summary.prod.length > 0;\n\n  function tearDown(): void {\n    const element = document.getElementById(bannerId);\n    if (element) {\n      element.remove();\n    }\n  }\n\n  function setupBannerStyles(bannerEl: HTMLElement): void {\n    bannerEl.style.display = 'flex';\n    bannerEl.style.background = '#7faaf0';\n    bannerEl.style.position = 'fixed';\n    bannerEl.style.bottom = '5px';\n    bannerEl.style.left = '5px';\n    bannerEl.style.padding = '.5em';\n    bannerEl.style.borderRadius = '5px';\n    bannerEl.style.alignItems = 'center';\n  }\n\n  function setupIconStyles(prependIcon: SVGElement, iconId: string): void {\n    prependIcon.setAttribute('width', '24');\n    prependIcon.setAttribute('id', iconId);\n    prependIcon.setAttribute('height', '24');\n    prependIcon.setAttribute('viewBox', '0 0 24 24');\n    prependIcon.setAttribute('fill', 'none');\n    prependIcon.style.marginLeft = '-6px';\n  }\n\n  function setupCloseBtn(): HTMLSpanElement {\n    const closeBtn = document.createElement('span');\n    closeBtn.style.cursor = 'pointer';\n    closeBtn.style.marginLeft = '16px';\n    closeBtn.style.fontSize = '24px';\n    closeBtn.innerHTML = ' &times;';\n    closeBtn.onclick = () => {\n      previouslyDismissed = true;\n      tearDown();\n    };\n    return closeBtn;\n  }\n\n  function setupLinkStyles(\n    learnMoreLink: HTMLAnchorElement,\n    learnMoreId: string\n  ): void {\n    learnMoreLink.setAttribute('id', learnMoreId);\n    learnMoreLink.innerText = 'Learn more';\n    learnMoreLink.href =\n      'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n    learnMoreLink.setAttribute('target', '__blank');\n    learnMoreLink.style.paddingLeft = '5px';\n    learnMoreLink.style.textDecoration = 'underline';\n  }\n\n  function setupDom(): void {\n    const banner = getOrCreateEl(bannerId);\n    const firebaseTextId = prefixedId('text');\n    const firebaseText: HTMLSpanElement =\n      document.getElementById(firebaseTextId) || document.createElement('span');\n    const learnMoreId = prefixedId('learnmore');\n    const learnMoreLink: HTMLAnchorElement =\n      (document.getElementById(learnMoreId) as HTMLAnchorElement) ||\n      document.createElement('a');\n    const prependIconId = prefixedId('preprendIcon');\n    const prependIcon: SVGElement =\n      (document.getElementById(\n        prependIconId\n      ) as HTMLOrSVGElement as SVGElement) ||\n      document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    if (banner.created) {\n      // update styles\n      const bannerEl = banner.element;\n      setupBannerStyles(bannerEl);\n      setupLinkStyles(learnMoreLink, learnMoreId);\n      const closeBtn = setupCloseBtn();\n      setupIconStyles(prependIcon, prependIconId);\n      bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n      document.body.appendChild(bannerEl);\n    }\n\n    if (showError) {\n      firebaseText.innerText = `Preview backend disconnected.`;\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n    } else {\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n      firebaseText.innerText = 'Preview backend running in this workspace.';\n    }\n    firebaseText.setAttribute('id', firebaseTextId);\n  }\n  if (document.readyState === 'loading') {\n    window.addEventListener('DOMContentLoaded', setupDom);\n  } else {\n    setupDom();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n\n/**\n * Domain name for firebase storage.\n */\nexport const DEFAULT_HOST = 'firebasestorage.googleapis.com';\n\n/**\n * The key in Firebase config json for the storage bucket.\n */\nexport const CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nexport const DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nexport const DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n\n/**\n * 1 second\n */\nexport const DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * This is the value of Number.MIN_SAFE_INTEGER, which is not well supported\n * enough for us to use it directly.\n */\nexport const MIN_SAFE_INTEGER = -9007199254740991;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nimport { CONFIG_STORAGE_BUCKET_KEY } from './constants';\n\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nexport class StorageError extends FirebaseError {\n  private readonly _baseMessage: string;\n  /**\n   * Stores custom error data unique to the `StorageError`.\n   */\n  customData: { serverResponse: string | null } = { serverResponse: null };\n\n  /**\n   * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n   *  added to the end of the message.\n   * @param message  - Error message.\n   * @param status_ - Corresponding HTTP Status Code\n   */\n  constructor(code: StorageErrorCode, message: string, private status_ = 0) {\n    super(\n      prependCode(code),\n      `Firebase Storage: ${message} (${prependCode(code)})`\n    );\n    this._baseMessage = this.message;\n    // Without this, `instanceof StorageError`, in tests for example,\n    // returns false.\n    Object.setPrototypeOf(this, StorageError.prototype);\n  }\n\n  get status(): number {\n    return this.status_;\n  }\n\n  set status(status: number) {\n    this.status_ = status;\n  }\n\n  /**\n   * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n   */\n  _codeEquals(code: StorageErrorCode): boolean {\n    return prependCode(code) === this.code;\n  }\n\n  /**\n   * Optional response message that was added by the server.\n   */\n  get serverResponse(): null | string {\n    return this.customData.serverResponse;\n  }\n\n  set serverResponse(serverResponse: string | null) {\n    this.customData.serverResponse = serverResponse;\n    if (this.customData.serverResponse) {\n      this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n    } else {\n      this.message = this._baseMessage;\n    }\n  }\n}\n\nexport const errors = {};\n\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nexport enum StorageErrorCode {\n  // Shared between all platforms\n  UNKNOWN = 'unknown',\n  OBJECT_NOT_FOUND = 'object-not-found',\n  BUCKET_NOT_FOUND = 'bucket-not-found',\n  PROJECT_NOT_FOUND = 'project-not-found',\n  QUOTA_EXCEEDED = 'quota-exceeded',\n  UNAUTHENTICATED = 'unauthenticated',\n  UNAUTHORIZED = 'unauthorized',\n  UNAUTHORIZED_APP = 'unauthorized-app',\n  RETRY_LIMIT_EXCEEDED = 'retry-limit-exceeded',\n  INVALID_CHECKSUM = 'invalid-checksum',\n  CANCELED = 'canceled',\n  // JS specific\n  INVALID_EVENT_NAME = 'invalid-event-name',\n  INVALID_URL = 'invalid-url',\n  INVALID_DEFAULT_BUCKET = 'invalid-default-bucket',\n  NO_DEFAULT_BUCKET = 'no-default-bucket',\n  CANNOT_SLICE_BLOB = 'cannot-slice-blob',\n  SERVER_FILE_WRONG_SIZE = 'server-file-wrong-size',\n  NO_DOWNLOAD_URL = 'no-download-url',\n  INVALID_ARGUMENT = 'invalid-argument',\n  INVALID_ARGUMENT_COUNT = 'invalid-argument-count',\n  APP_DELETED = 'app-deleted',\n  INVALID_ROOT_OPERATION = 'invalid-root-operation',\n  INVALID_FORMAT = 'invalid-format',\n  INTERNAL_ERROR = 'internal-error',\n  UNSUPPORTED_ENVIRONMENT = 'unsupported-environment'\n}\n\nexport function prependCode(code: StorageErrorCode): string {\n  return 'storage/' + code;\n}\n\nexport function unknown(): StorageError {\n  const message =\n    'An unknown error occurred, please check the error payload for ' +\n    'server response.';\n  return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\n\nexport function objectNotFound(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.OBJECT_NOT_FOUND,\n    \"Object '\" + path + \"' does not exist.\"\n  );\n}\n\nexport function bucketNotFound(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.BUCKET_NOT_FOUND,\n    \"Bucket '\" + bucket + \"' does not exist.\"\n  );\n}\n\nexport function projectNotFound(project: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.PROJECT_NOT_FOUND,\n    \"Project '\" + project + \"' does not exist.\"\n  );\n}\n\nexport function quotaExceeded(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.QUOTA_EXCEEDED,\n    \"Quota for bucket '\" +\n      bucket +\n      \"' exceeded, please view quota on \" +\n      'https://firebase.google.com/pricing/.'\n  );\n}\n\nexport function unauthenticated(): StorageError {\n  const message =\n    'User is not authenticated, please authenticate using Firebase ' +\n    'Authentication and try again.';\n  return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\n\nexport function unauthorizedApp(): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED_APP,\n    'This app does not have permission to access Firebase Storage on this project.'\n  );\n}\n\nexport function unauthorized(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED,\n    \"User does not have permission to access '\" + path + \"'.\"\n  );\n}\n\nexport function retryLimitExceeded(): StorageError {\n  return new StorageError(\n    StorageErrorCode.RETRY_LIMIT_EXCEEDED,\n    'Max retry time for operation exceeded, please try again.'\n  );\n}\n\nexport function invalidChecksum(\n  path: string,\n  checksum: string,\n  calculated: string\n): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_CHECKSUM,\n    \"Uploaded/downloaded object '\" +\n      path +\n      \"' has checksum '\" +\n      checksum +\n      \"' which does not match '\" +\n      calculated +\n      \"'. Please retry the upload/download.\"\n  );\n}\n\nexport function canceled(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANCELED,\n    'User canceled the upload/download.'\n  );\n}\n\nexport function invalidEventName(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_EVENT_NAME,\n    \"Invalid event name '\" + name + \"'.\"\n  );\n}\n\nexport function invalidUrl(url: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_URL,\n    \"Invalid URL '\" + url + \"'.\"\n  );\n}\n\nexport function invalidDefaultBucket(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_DEFAULT_BUCKET,\n    \"Invalid default bucket '\" + bucket + \"'.\"\n  );\n}\n\nexport function noDefaultBucket(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DEFAULT_BUCKET,\n    'No default bucket ' +\n      \"found. Did you set the '\" +\n      CONFIG_STORAGE_BUCKET_KEY +\n      \"' property when initializing the app?\"\n  );\n}\n\nexport function cannotSliceBlob(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANNOT_SLICE_BLOB,\n    'Cannot slice blob for upload. Please retry the upload.'\n  );\n}\n\nexport function serverFileWrongSize(): StorageError {\n  return new StorageError(\n    StorageErrorCode.SERVER_FILE_WRONG_SIZE,\n    'Server recorded incorrect upload file size, please retry the upload.'\n  );\n}\n\nexport function noDownloadURL(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DOWNLOAD_URL,\n    'The given file does not have any download URLs.'\n  );\n}\n\nexport function missingPolyFill(polyFill: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n    `${polyFill} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`\n  );\n}\n\n/**\n * @internal\n */\nexport function invalidArgument(message: string): StorageError {\n  return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\n\nexport function invalidArgumentCount(\n  argMin: number,\n  argMax: number,\n  fnName: string,\n  real: number\n): StorageError {\n  let countPart;\n  let plural;\n  if (argMin === argMax) {\n    countPart = argMin;\n    plural = argMin === 1 ? 'argument' : 'arguments';\n  } else {\n    countPart = 'between ' + argMin + ' and ' + argMax;\n    plural = 'arguments';\n  }\n  return new StorageError(\n    StorageErrorCode.INVALID_ARGUMENT_COUNT,\n    'Invalid argument count in `' +\n      fnName +\n      '`: Expected ' +\n      countPart +\n      ' ' +\n      plural +\n      ', received ' +\n      real +\n      '.'\n  );\n}\n\nexport function appDeleted(): StorageError {\n  return new StorageError(\n    StorageErrorCode.APP_DELETED,\n    'The Firebase app was deleted.'\n  );\n}\n\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nexport function invalidRootOperation(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_ROOT_OPERATION,\n    \"The operation '\" +\n      name +\n      \"' cannot be performed on a root reference, create a non-root \" +\n      \"reference using child, such as .child('file.png').\"\n  );\n}\n\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nexport function invalidFormat(format: string, message: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_FORMAT,\n    \"String does not match format '\" + format + \"': \" + message\n  );\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function unsupportedEnvironment(message: string): StorageError {\n  throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, message);\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function internalError(message: string): StorageError {\n  throw new StorageError(\n    StorageErrorCode.INTERNAL_ERROR,\n    'Internal error: ' + message\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Network headers */\nexport type Headers = Record<string, string>;\n\n/** Response type exposed by the networking APIs. */\nexport type ConnectionType =\n  | string\n  | ArrayBuffer\n  | Blob\n  | ReadableStream<Uint8Array>;\n\n/**\n * A lightweight wrapper around XMLHttpRequest with a\n * goog.net.XhrIo-like interface.\n *\n * You can create a new connection by invoking `newTextConnection()`,\n * `newBytesConnection()` or `newStreamConnection()`.\n */\nexport interface Connection<T extends ConnectionType> {\n  /**\n   * Sends a request to the provided URL.\n   *\n   * This method never rejects its promise. In case of encountering an error,\n   * it sets an error code internally which can be accessed by calling\n   * getErrorCode() by callers.\n   */\n  send(\n    url: string,\n    method: string,\n    isUsingEmulator: boolean,\n    body?: ArrayBufferView | Blob | string | null,\n    headers?: Headers\n  ): Promise<void>;\n\n  getErrorCode(): ErrorCode;\n\n  getStatus(): number;\n\n  getResponse(): T;\n\n  getErrorText(): string;\n\n  /**\n   * Abort the request.\n   */\n  abort(): void;\n\n  getResponseHeader(header: string): string | null;\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nexport enum ErrorCode {\n  NO_ERROR = 0,\n  NETWORK_ERROR = 1,\n  ABORT = 2\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport firebase from '@firebase/app-compat';\nimport { _FirebaseNamespace } from '@firebase/app-types/private';\nimport {\n  StringFormat,\n  _TaskEvent as TaskEvent,\n  _TaskState as TaskState\n} from '@firebase/storage';\n\nimport { ReferenceCompat } from './reference';\nimport { StorageServiceCompat } from './service';\nimport * as types from '@firebase/storage-types';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\n/**\n * Type constant for Firebase Storage.\n */\nconst STORAGE_TYPE = 'storage-compat';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): types.FirebaseStorage {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const storageExp = container\n    .getProvider('storage')\n    .getImmediate({ identifier: url });\n\n  const storageServiceCompat: StorageServiceCompat = new StorageServiceCompat(\n    app,\n    storageExp\n  );\n  return storageServiceCompat;\n}\n\nexport function registerStorage(instance: _FirebaseNamespace): void {\n  const namespaceExports = {\n    // no-inline\n    TaskState,\n    TaskEvent,\n    StringFormat,\n    Storage: StorageServiceCompat,\n    Reference: ReferenceCompat\n  };\n  instance.INTERNAL.registerComponent(\n    new Component(STORAGE_TYPE, factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n\n  instance.registerVersion(name, version);\n}\n\nregisterStorage(firebase as unknown as _FirebaseNamespace);\n\n/**\n * Define extension behavior for `registerStorage`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    storage?: {\n      (app?: FirebaseApp, url?: string): types.FirebaseStorage;\n      Storage: typeof types.FirebaseStorage;\n\n      StringFormat: {\n        BASE64: types.StringFormat;\n        BASE64URL: types.StringFormat;\n        DATA_URL: types.StringFormat;\n        RAW: types.StringFormat;\n      };\n      TaskEvent: {\n        STATE_CHANGED: types.TaskEvent;\n      };\n      TaskState: {\n        CANCELED: types.TaskState;\n        ERROR: types.TaskState;\n        PAUSED: types.TaskState;\n        RUNNING: types.TaskState;\n        SUCCESS: types.TaskState;\n      };\n    };\n  }\n  interface FirebaseApp {\n    storage?(storageBucket?: string): types.FirebaseStorage;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functionality related to the parsing/composition of bucket/\n * object location.\n */\n\nimport { invalidDefaultBucket, invalidUrl } from './error';\nimport { DEFAULT_HOST } from './constants';\n\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nexport class Location {\n  private path_: string;\n\n  constructor(public readonly bucket: string, path: string) {\n    this.path_ = path;\n  }\n\n  get path(): string {\n    return this.path_;\n  }\n\n  get isRoot(): boolean {\n    return this.path.length === 0;\n  }\n\n  fullServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n  }\n\n  bucketOnlyServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o';\n  }\n\n  static makeFromBucketSpec(bucketString: string, host: string): Location {\n    let bucketLocation;\n    try {\n      bucketLocation = Location.makeFromUrl(bucketString, host);\n    } catch (e) {\n      // Not valid URL, use as-is. This lets you put bare bucket names in\n      // config.\n      return new Location(bucketString, '');\n    }\n    if (bucketLocation.path === '') {\n      return bucketLocation;\n    } else {\n      throw invalidDefaultBucket(bucketString);\n    }\n  }\n\n  static makeFromUrl(url: string, host: string): Location {\n    let location: Location | null = null;\n    const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n\n    function gsModify(loc: Location): void {\n      if (loc.path.charAt(loc.path.length - 1) === '/') {\n        loc.path_ = loc.path_.slice(0, -1);\n      }\n    }\n    const gsPath = '(/(.*))?$';\n    const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n    const gsIndices = { bucket: 1, path: 3 };\n\n    function httpModify(loc: Location): void {\n      loc.path_ = decodeURIComponent(loc.path);\n    }\n    const version = 'v[A-Za-z0-9_]+';\n    const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n    const firebaseStoragePath = '(/([^?#]*).*)?$';\n    const firebaseStorageRegExp = new RegExp(\n      `^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`,\n      'i'\n    );\n    const firebaseStorageIndices = { bucket: 1, path: 3 };\n\n    const cloudStorageHost =\n      host === DEFAULT_HOST\n        ? '(?:storage.googleapis.com|storage.cloud.google.com)'\n        : host;\n    const cloudStoragePath = '([^?#]*)';\n    const cloudStorageRegExp = new RegExp(\n      `^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`,\n      'i'\n    );\n    const cloudStorageIndices = { bucket: 1, path: 2 };\n\n    const groups = [\n      { regex: gsRegex, indices: gsIndices, postModify: gsModify },\n      {\n        regex: firebaseStorageRegExp,\n        indices: firebaseStorageIndices,\n        postModify: httpModify\n      },\n      {\n        regex: cloudStorageRegExp,\n        indices: cloudStorageIndices,\n        postModify: httpModify\n      }\n    ];\n    for (let i = 0; i < groups.length; i++) {\n      const group = groups[i];\n      const captures = group.regex.exec(url);\n      if (captures) {\n        const bucketValue = captures[group.indices.bucket];\n        let pathValue = captures[group.indices.path];\n        if (!pathValue) {\n          pathValue = '';\n        }\n        location = new Location(bucketValue, pathValue);\n        group.postModify(location);\n        break;\n      }\n    }\n    if (location == null) {\n      throw invalidUrl(url);\n    }\n    return location;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Request } from './request';\n\n/**\n * A request whose promise always fails.\n */\nexport class FailRequest<T> implements Request<T> {\n  promise_: Promise<T>;\n\n  constructor(error: StorageError) {\n    this.promise_ = Promise.reject<T>(error);\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<T> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(_appDelete = false): void {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { invalidArgument } from './error';\n\nexport function isJustDef<T>(p: T | null | undefined): p is T | null {\n  return p !== void 0;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function isFunction(p: unknown): p is Function {\n  return typeof p === 'function';\n}\n\nexport function isNonArrayObject(p: unknown): boolean {\n  return typeof p === 'object' && !Array.isArray(p);\n}\n\nexport function isString(p: unknown): p is string {\n  return typeof p === 'string' || p instanceof String;\n}\n\nexport function isNativeBlob(p: unknown): p is Blob {\n  return isNativeBlobDefined() && p instanceof Blob;\n}\n\nexport function isNativeBlobDefined(): boolean {\n  return typeof Blob !== 'undefined';\n}\n\nexport function validateNumber(\n  argument: string,\n  minValue: number,\n  maxValue: number,\n  value: number\n): void {\n  if (value < minValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${minValue} or greater.`\n    );\n  }\n  if (value > maxValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${maxValue} or less.`\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functions to create and manipulate URLs for the server API.\n */\nimport { UrlParams } from './requestinfo';\n\nexport function makeUrl(\n  urlPart: string,\n  host: string,\n  protocol: string\n): string {\n  let origin = host;\n  if (protocol == null) {\n    origin = `https://${host}`;\n  }\n  return `${protocol}://${origin}/v0${urlPart}`;\n}\n\nexport function makeQueryString(params: UrlParams): string {\n  const encode = encodeURIComponent;\n  let queryPart = '?';\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      const nextPart = encode(key) + '=' + encode(params[key]);\n      queryPart = queryPart + nextPart + '&';\n    }\n  }\n\n  // Chop off the extra '&' or '?' on the end\n  queryPart = queryPart.slice(0, -1);\n  return queryPart;\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nexport function isRetryStatusCode(\n  status: number,\n  additionalRetryCodes: number[]\n): boolean {\n  // The codes for which to retry came from this page:\n  // https://cloud.google.com/storage/docs/exponential-backoff\n  const isFiveHundredCode = status >= 500 && status < 600;\n  const extraRetryCodes = [\n    // Request Timeout: web server didn't receive full request in time.\n    408,\n    // Too Many Requests: you're getting rate-limited, basically.\n    429\n  ];\n  const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n  const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n  return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods used to actually send HTTP requests from\n * abstract representations.\n */\n\nimport { id as backoffId, start, stop } from './backoff';\nimport { appDeleted, canceled, retryLimitExceeded, unknown } from './error';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RequestHandler, RequestInfo } from './requestinfo';\nimport { isJustDef } from './type';\nimport { makeQueryString } from './url';\nimport { Connection, ErrorCode, Headers, ConnectionType } from './connection';\nimport { isRetryStatusCode } from './utils';\n\nexport interface Request<T> {\n  getPromise(): Promise<T>;\n\n  /**\n   * Cancels the request. IMPORTANT: the promise may still be resolved with an\n   * appropriate value (if the request is finished before you call this method,\n   * but the promise has not yet been resolved), so don't just assume it will be\n   * rejected if you call this function.\n   * @param appDelete - True if the cancelation came from the app being deleted.\n   */\n  cancel(appDelete?: boolean): void;\n}\n\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest<I extends ConnectionType, O> implements Request<O> {\n  private pendingConnection_: Connection<I> | null = null;\n  private backoffId_: backoffId | null = null;\n  private resolve_!: (value?: O | PromiseLike<O>) => void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private reject_!: (reason?: any) => void;\n  private canceled_: boolean = false;\n  private appDelete_: boolean = false;\n  private promise_: Promise<O>;\n\n  constructor(\n    private url_: string,\n    private method_: string,\n    private headers_: Headers,\n    private body_: string | Blob | Uint8Array | null,\n    private successCodes_: number[],\n    private additionalRetryCodes_: number[],\n    private callback_: RequestHandler<I, O>,\n    private errorCallback_: ErrorHandler | null,\n    private timeout_: number,\n    private progressCallback_: ((p1: number, p2: number) => void) | null,\n    private connectionFactory_: () => Connection<I>,\n    private retry = true,\n    private isUsingEmulator = false\n  ) {\n    this.promise_ = new Promise((resolve, reject) => {\n      this.resolve_ = resolve as (value?: O | PromiseLike<O>) => void;\n      this.reject_ = reject;\n      this.start_();\n    });\n  }\n\n  /**\n   * Actually starts the retry loop.\n   */\n  private start_(): void {\n    const doTheRequest: (\n      backoffCallback: (success: boolean, ...p2: unknown[]) => void,\n      canceled: boolean\n    ) => void = (backoffCallback, canceled) => {\n      if (canceled) {\n        backoffCallback(false, new RequestEndStatus(false, null, true));\n        return;\n      }\n      const connection = this.connectionFactory_();\n      this.pendingConnection_ = connection;\n\n      const progressListener: (\n        progressEvent: ProgressEvent\n      ) => void = progressEvent => {\n        const loaded = progressEvent.loaded;\n        const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n        if (this.progressCallback_ !== null) {\n          this.progressCallback_(loaded, total);\n        }\n      };\n      if (this.progressCallback_ !== null) {\n        connection.addUploadProgressListener(progressListener);\n      }\n\n      // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      connection\n        .send(\n          this.url_,\n          this.method_,\n          this.isUsingEmulator,\n          this.body_,\n          this.headers_\n        )\n        .then(() => {\n          if (this.progressCallback_ !== null) {\n            connection.removeUploadProgressListener(progressListener);\n          }\n          this.pendingConnection_ = null;\n          const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n          const status = connection.getStatus();\n          if (\n            !hitServer ||\n            (isRetryStatusCode(status, this.additionalRetryCodes_) &&\n              this.retry)\n          ) {\n            const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n            backoffCallback(\n              false,\n              new RequestEndStatus(false, null, wasCanceled)\n            );\n            return;\n          }\n          const successCode = this.successCodes_.indexOf(status) !== -1;\n          backoffCallback(true, new RequestEndStatus(successCode, connection));\n        });\n    };\n\n    /**\n     * @param requestWentThrough - True if the request eventually went\n     *     through, false if it hit the retry limit or was canceled.\n     */\n    const backoffDone: (\n      requestWentThrough: boolean,\n      status: RequestEndStatus<I>\n    ) => void = (requestWentThrough, status) => {\n      const resolve = this.resolve_;\n      const reject = this.reject_;\n      const connection = status.connection as Connection<I>;\n      if (status.wasSuccessCode) {\n        try {\n          const result = this.callback_(connection, connection.getResponse());\n          if (isJustDef(result)) {\n            resolve(result);\n          } else {\n            resolve();\n          }\n        } catch (e) {\n          reject(e);\n        }\n      } else {\n        if (connection !== null) {\n          const err = unknown();\n          err.serverResponse = connection.getErrorText();\n          if (this.errorCallback_) {\n            reject(this.errorCallback_(connection, err));\n          } else {\n            reject(err);\n          }\n        } else {\n          if (status.canceled) {\n            const err = this.appDelete_ ? appDeleted() : canceled();\n            reject(err);\n          } else {\n            const err = retryLimitExceeded();\n            reject(err);\n          }\n        }\n      }\n    };\n    if (this.canceled_) {\n      backoffDone(false, new RequestEndStatus(false, null, true));\n    } else {\n      this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n    }\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<O> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(appDelete?: boolean): void {\n    this.canceled_ = true;\n    this.appDelete_ = appDelete || false;\n    if (this.backoffId_ !== null) {\n      stop(this.backoffId_);\n    }\n    if (this.pendingConnection_ !== null) {\n      this.pendingConnection_.abort();\n    }\n  }\n}\n\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nexport class RequestEndStatus<I extends ConnectionType> {\n  /**\n   * True if the request was canceled.\n   */\n  canceled: boolean;\n\n  constructor(\n    public wasSuccessCode: boolean,\n    public connection: Connection<I> | null,\n    canceled?: boolean\n  ) {\n    this.canceled = !!canceled;\n  }\n}\n\nexport function addAuthHeader_(\n  headers: Headers,\n  authToken: string | null\n): void {\n  if (authToken !== null && authToken.length > 0) {\n    headers['Authorization'] = 'Firebase ' + authToken;\n  }\n}\n\nexport function addVersionHeader_(\n  headers: Headers,\n  firebaseVersion?: string\n): void {\n  headers['X-Firebase-Storage-Version'] =\n    'webjs/' + (firebaseVersion ?? 'AppManager');\n}\n\nexport function addGmpidHeader_(headers: Headers, appId: string | null): void {\n  if (appId) {\n    headers['X-Firebase-GMPID'] = appId;\n  }\n}\n\nexport function addAppCheckHeader_(\n  headers: Headers,\n  appCheckToken: string | null\n): void {\n  if (appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n}\n\nexport function makeRequest<I extends ConnectionType, O>(\n  requestInfo: RequestInfo<I, O>,\n  appId: string | null,\n  authToken: string | null,\n  appCheckToken: string | null,\n  requestFactory: () => Connection<I>,\n  firebaseVersion?: string,\n  retry = true,\n  isUsingEmulator = false\n): Request<O> {\n  const queryPart = makeQueryString(requestInfo.urlParams);\n  const url = requestInfo.url + queryPart;\n  const headers = Object.assign({}, requestInfo.headers);\n  addGmpidHeader_(headers, appId);\n  addAuthHeader_(headers, authToken);\n  addVersionHeader_(headers, firebaseVersion);\n  addAppCheckHeader_(headers, appCheckToken);\n  return new NetworkRequest<I, O>(\n    url,\n    requestInfo.method,\n    headers,\n    requestInfo.body,\n    requestInfo.successCodes,\n    requestInfo.additionalRetryCodes,\n    requestInfo.handler,\n    requestInfo.errorHandler,\n    requestInfo.timeout,\n    requestInfo.progressCallback,\n    requestFactory,\n    retry,\n    isUsingEmulator\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Provides a method for running a function with exponential\n * backoff.\n */\ntype id = (p1: boolean) => void;\n\nexport { id };\n\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nexport function start(\n  doRequest: (\n    onRequestComplete: (success: boolean) => void,\n    canceled: boolean\n  ) => void,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  backoffCompleteCb: (...args: any[]) => unknown,\n  timeout: number\n): id {\n  // TODO(andysoto): make this code cleaner (probably refactor into an actual\n  // type instead of a bunch of functions with state shared in the closure)\n  let waitSeconds = 1;\n  // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n  // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let retryTimeoutId: any = null;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let globalTimeoutId: any = null;\n  let hitTimeout = false;\n  let cancelState = 0;\n\n  function canceled(): boolean {\n    return cancelState === 2;\n  }\n  let triggeredCallback = false;\n\n  function triggerCallback(...args: any[]): void {\n    if (!triggeredCallback) {\n      triggeredCallback = true;\n      backoffCompleteCb.apply(null, args);\n    }\n  }\n\n  function callWithDelay(millis: number): void {\n    retryTimeoutId = setTimeout(() => {\n      retryTimeoutId = null;\n      doRequest(responseHandler, canceled());\n    }, millis);\n  }\n\n  function clearGlobalTimeout(): void {\n    if (globalTimeoutId) {\n      clearTimeout(globalTimeoutId);\n    }\n  }\n\n  function responseHandler(success: boolean, ...args: any[]): void {\n    if (triggeredCallback) {\n      clearGlobalTimeout();\n      return;\n    }\n    if (success) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    const mustStop = canceled() || hitTimeout;\n    if (mustStop) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    if (waitSeconds < 64) {\n      /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n      waitSeconds *= 2;\n    }\n    let waitMillis;\n    if (cancelState === 1) {\n      cancelState = 2;\n      waitMillis = 0;\n    } else {\n      waitMillis = (waitSeconds + Math.random()) * 1000;\n    }\n    callWithDelay(waitMillis);\n  }\n  let stopped = false;\n\n  function stop(wasTimeout: boolean): void {\n    if (stopped) {\n      return;\n    }\n    stopped = true;\n    clearGlobalTimeout();\n    if (triggeredCallback) {\n      return;\n    }\n    if (retryTimeoutId !== null) {\n      if (!wasTimeout) {\n        cancelState = 2;\n      }\n      clearTimeout(retryTimeoutId);\n      callWithDelay(0);\n    } else {\n      if (!wasTimeout) {\n        cancelState = 1;\n      }\n    }\n  }\n  callWithDelay(0);\n  globalTimeoutId = setTimeout(() => {\n    hitTimeout = true;\n    stop(true);\n  }, timeout);\n  return stop;\n}\n\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nexport function stop(id: id): void {\n  id(false);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Some methods copied from goog.fs.\n * We don't include goog.fs because it pulls in a bunch of Deferred code that\n * bloats the size of the released binary.\n */\nimport { isNativeBlobDefined } from './type';\nimport { StorageErrorCode, StorageError } from './error';\n\nfunction getBlobBuilder(): typeof IBlobBuilder | undefined {\n  if (typeof BlobBuilder !== 'undefined') {\n    return BlobBuilder;\n  } else if (typeof WebKitBlobBuilder !== 'undefined') {\n    return WebKitBlobBuilder;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nexport function getBlob(...args: Array<string | Blob | ArrayBuffer>): Blob {\n  const BlobBuilder = getBlobBuilder();\n  if (BlobBuilder !== undefined) {\n    const bb = new BlobBuilder();\n    for (let i = 0; i < args.length; i++) {\n      bb.append(args[i]);\n    }\n    return bb.getBlob();\n  } else {\n    if (isNativeBlobDefined()) {\n      return new Blob(args);\n    } else {\n      throw new StorageError(\n        StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n        \"This browser doesn't seem to support creating Blobs\"\n      );\n    }\n  }\n}\n\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nexport function sliceBlob(blob: Blob, start: number, end: number): Blob | null {\n  if (blob.webkitSlice) {\n    return blob.webkitSlice(start, end);\n  } else if (blob.mozSlice) {\n    return blob.mozSlice(start, end);\n  } else if (blob.slice) {\n    return blob.slice(start, end);\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { missingPolyFill } from '../../implementation/error';\n\n/** Converts a Base64 encoded string to a binary string. */\nexport function decodeBase64(encoded: string): string {\n  if (typeof atob === 'undefined') {\n    throw missingPolyFill('base-64');\n  }\n  return atob(encoded);\n}\n\nexport function decodeUint8Array(data: Uint8Array): string {\n  return new TextDecoder().decode(data);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { unknown, invalidFormat } from './error';\nimport { decodeBase64 } from '../platform/base64';\n\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport type StringFormat = (typeof StringFormat)[keyof typeof StringFormat];\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport const StringFormat = {\n  /**\n   * Indicates the string should be interpreted \"raw\", that is, as normal text.\n   * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n   * sequence.\n   * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n   * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n   */\n  RAW: 'raw',\n  /**\n   * Indicates the string should be interpreted as base64-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64: 'base64',\n  /**\n   * Indicates the string should be interpreted as base64url-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64URL: 'base64url',\n  /**\n   * Indicates the string is a data URL, such as one obtained from\n   * canvas.toDataURL().\n   * Example: the string 'data:application/octet-stream;base64,aaaa'\n   * becomes the byte sequence\n   * 69 a6 9a\n   * (the content-type \"application/octet-stream\" is also applied, but can\n   * be overridden in the metadata object).\n   */\n  DATA_URL: 'data_url'\n} as const;\n\nexport class StringData {\n  contentType: string | null;\n\n  constructor(public data: Uint8Array, contentType?: string | null) {\n    this.contentType = contentType || null;\n  }\n}\n\n/**\n * @internal\n */\nexport function dataFromString(\n  format: StringFormat,\n  stringData: string\n): StringData {\n  switch (format) {\n    case StringFormat.RAW:\n      return new StringData(utf8Bytes_(stringData));\n    case StringFormat.BASE64:\n    case StringFormat.BASE64URL:\n      return new StringData(base64Bytes_(format, stringData));\n    case StringFormat.DATA_URL:\n      return new StringData(\n        dataURLBytes_(stringData),\n        dataURLContentType_(stringData)\n      );\n    default:\n    // do nothing\n  }\n\n  // assert(false);\n  throw unknown();\n}\n\nexport function utf8Bytes_(value: string): Uint8Array {\n  const b: number[] = [];\n  for (let i = 0; i < value.length; i++) {\n    let c = value.charCodeAt(i);\n    if (c <= 127) {\n      b.push(c);\n    } else {\n      if (c <= 2047) {\n        b.push(192 | (c >> 6), 128 | (c & 63));\n      } else {\n        if ((c & 64512) === 55296) {\n          // The start of a surrogate pair.\n          const valid =\n            i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n          if (!valid) {\n            // The second surrogate wasn't there.\n            b.push(239, 191, 189);\n          } else {\n            const hi = c;\n            const lo = value.charCodeAt(++i);\n            c = 65536 | ((hi & 1023) << 10) | (lo & 1023);\n            b.push(\n              240 | (c >> 18),\n              128 | ((c >> 12) & 63),\n              128 | ((c >> 6) & 63),\n              128 | (c & 63)\n            );\n          }\n        } else {\n          if ((c & 64512) === 56320) {\n            // Invalid low surrogate.\n            b.push(239, 191, 189);\n          } else {\n            b.push(224 | (c >> 12), 128 | ((c >> 6) & 63), 128 | (c & 63));\n          }\n        }\n      }\n    }\n  }\n  return new Uint8Array(b);\n}\n\nexport function percentEncodedBytes_(value: string): Uint8Array {\n  let decoded;\n  try {\n    decoded = decodeURIComponent(value);\n  } catch (e) {\n    throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n  }\n  return utf8Bytes_(decoded);\n}\n\nexport function base64Bytes_(format: StringFormat, value: string): Uint8Array {\n  switch (format) {\n    case StringFormat.BASE64: {\n      const hasMinus = value.indexOf('-') !== -1;\n      const hasUnder = value.indexOf('_') !== -1;\n      if (hasMinus || hasUnder) {\n        const invalidChar = hasMinus ? '-' : '_';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" +\n            invalidChar +\n            \"' found: is it base64url encoded?\"\n        );\n      }\n      break;\n    }\n    case StringFormat.BASE64URL: {\n      const hasPlus = value.indexOf('+') !== -1;\n      const hasSlash = value.indexOf('/') !== -1;\n      if (hasPlus || hasSlash) {\n        const invalidChar = hasPlus ? '+' : '/';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\"\n        );\n      }\n      value = value.replace(/-/g, '+').replace(/_/g, '/');\n      break;\n    }\n    default:\n    // do nothing\n  }\n  let bytes;\n  try {\n    bytes = decodeBase64(value);\n  } catch (e) {\n    if ((e as Error).message.includes('polyfill')) {\n      throw e;\n    }\n    throw invalidFormat(format, 'Invalid character found');\n  }\n  const array = new Uint8Array(bytes.length);\n  for (let i = 0; i < bytes.length; i++) {\n    array[i] = bytes.charCodeAt(i);\n  }\n  return array;\n}\n\nclass DataURLParts {\n  base64: boolean = false;\n  contentType: string | null = null;\n  rest: string;\n\n  constructor(dataURL: string) {\n    const matches = dataURL.match(/^data:([^,]+)?,/);\n    if (matches === null) {\n      throw invalidFormat(\n        StringFormat.DATA_URL,\n        \"Must be formatted 'data:[<mediatype>][;base64],<data>\"\n      );\n    }\n    const middle = matches[1] || null;\n    if (middle != null) {\n      this.base64 = endsWith(middle, ';base64');\n      this.contentType = this.base64\n        ? middle.substring(0, middle.length - ';base64'.length)\n        : middle;\n    }\n    this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n  }\n}\n\nexport function dataURLBytes_(dataUrl: string): Uint8Array {\n  const parts = new DataURLParts(dataUrl);\n  if (parts.base64) {\n    return base64Bytes_(StringFormat.BASE64, parts.rest);\n  } else {\n    return percentEncodedBytes_(parts.rest);\n  }\n}\n\nexport function dataURLContentType_(dataUrl: string): string | null {\n  const parts = new DataURLParts(dataUrl);\n  return parts.contentType;\n}\n\nfunction endsWith(s: string, end: string): boolean {\n  const longEnough = s.length >= end.length;\n  if (!longEnough) {\n    return false;\n  }\n\n  return s.substring(s.length - end.length) === end;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @file Provides a Blob-like wrapper for various binary types (including the\n * native Blob type). This makes it possible to upload types like ArrayBuffers,\n * making uploads possible in environments without the native Blob type.\n */\nimport { sliceBlob, getBlob } from './fs';\nimport { StringFormat, dataFromString } from './string';\nimport { isNativeBlob, isNativeBlobDefined, isString } from './type';\n\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nexport class FbsBlob {\n  private data_!: Blob | Uint8Array;\n  private size_: number;\n  private type_: string;\n\n  constructor(data: Blob | Uint8Array | ArrayBuffer, elideCopy?: boolean) {\n    let size: number = 0;\n    let blobType: string = '';\n    if (isNativeBlob(data)) {\n      this.data_ = data as Blob;\n      size = (data as Blob).size;\n      blobType = (data as Blob).type;\n    } else if (data instanceof ArrayBuffer) {\n      if (elideCopy) {\n        this.data_ = new Uint8Array(data);\n      } else {\n        this.data_ = new Uint8Array(data.byteLength);\n        this.data_.set(new Uint8Array(data));\n      }\n      size = this.data_.length;\n    } else if (data instanceof Uint8Array) {\n      if (elideCopy) {\n        this.data_ = data as Uint8Array;\n      } else {\n        this.data_ = new Uint8Array(data.length);\n        this.data_.set(data as Uint8Array);\n      }\n      size = data.length;\n    }\n    this.size_ = size;\n    this.type_ = blobType;\n  }\n\n  size(): number {\n    return this.size_;\n  }\n\n  type(): string {\n    return this.type_;\n  }\n\n  slice(startByte: number, endByte: number): FbsBlob | null {\n    if (isNativeBlob(this.data_)) {\n      const realBlob = this.data_ as Blob;\n      const sliced = sliceBlob(realBlob, startByte, endByte);\n      if (sliced === null) {\n        return null;\n      }\n      return new FbsBlob(sliced);\n    } else {\n      const slice = new Uint8Array(\n        (this.data_ as Uint8Array).buffer,\n        startByte,\n        endByte - startByte\n      );\n      return new FbsBlob(slice, true);\n    }\n  }\n\n  static getBlob(...args: Array<string | FbsBlob>): FbsBlob | null {\n    if (isNativeBlobDefined()) {\n      const blobby: Array<Blob | Uint8Array | string> = args.map(\n        (val: string | FbsBlob): Blob | Uint8Array | string => {\n          if (val instanceof FbsBlob) {\n            return val.data_;\n          } else {\n            return val;\n          }\n        }\n      );\n      return new FbsBlob(getBlob.apply(null, blobby));\n    } else {\n      const uint8Arrays: Uint8Array[] = args.map(\n        (val: string | FbsBlob): Uint8Array => {\n          if (isString(val)) {\n            return dataFromString(StringFormat.RAW, val as string).data;\n          } else {\n            // Blobs don't exist, so this has to be a Uint8Array.\n            return (val as FbsBlob).data_ as Uint8Array;\n          }\n        }\n      );\n      let finalLength = 0;\n      uint8Arrays.forEach((array: Uint8Array): void => {\n        finalLength += array.byteLength;\n      });\n      const merged = new Uint8Array(finalLength);\n      let index = 0;\n      uint8Arrays.forEach((array: Uint8Array) => {\n        for (let i = 0; i < array.length; i++) {\n          merged[index++] = array[i];\n        }\n      });\n      return new FbsBlob(merged, true);\n    }\n  }\n\n  uploadData(): Blob | Uint8Array {\n    return this.data_;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isNonArrayObject } from './type';\n\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nexport function jsonObjectOrNull(\n  s: string\n): { [name: string]: unknown } | null {\n  let obj;\n  try {\n    obj = JSON.parse(s);\n  } catch (e) {\n    return null;\n  }\n  if (isNonArrayObject(obj)) {\n    return obj;\n  } else {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n\n/**\n * @return Null if the path is already at the root.\n */\nexport function parent(path: string): string | null {\n  if (path.length === 0) {\n    return null;\n  }\n  const index = path.lastIndexOf('/');\n  if (index === -1) {\n    return '';\n  }\n  const newPath = path.slice(0, index);\n  return newPath;\n}\n\nexport function child(path: string, childPath: string): string {\n  const canonicalChildPath = childPath\n    .split('/')\n    .filter(component => component.length > 0)\n    .join('/');\n  if (path.length === 0) {\n    return canonicalChildPath;\n  } else {\n    return path + '/' + canonicalChildPath;\n  }\n}\n\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nexport function lastComponent(path: string): string {\n  const index = path.lastIndexOf('/', path.length - 2);\n  if (index === -1) {\n    return path;\n  } else {\n    return path.slice(index + 1);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the metadata format\n */\nimport { Metadata } from '../metadata';\n\nimport { jsonObjectOrNull } from './json';\nimport { Location } from './location';\nimport { lastComponent } from './path';\nimport { isString } from './type';\nimport { makeUrl, makeQueryString } from './url';\nimport { Reference } from '../reference';\nimport { FirebaseStorageImpl } from '../service';\n\nexport function noXform_<T>(metadata: Metadata, value: T): T {\n  return value;\n}\n\nclass Mapping<T> {\n  local: string;\n  writable: boolean;\n  xform: (p1: Metadata, p2?: T) => T | undefined;\n\n  constructor(\n    public server: string,\n    local?: string | null,\n    writable?: boolean,\n    xform?: ((p1: Metadata, p2?: T) => T | undefined) | null\n  ) {\n    this.local = local || server;\n    this.writable = !!writable;\n    this.xform = xform || noXform_;\n  }\n}\ntype Mappings = Array<Mapping<string> | Mapping<number>>;\n\nexport { Mappings };\n\nlet mappings_: Mappings | null = null;\n\nexport function xformPath(fullPath: string | undefined): string | undefined {\n  if (!isString(fullPath) || fullPath.length < 2) {\n    return fullPath;\n  } else {\n    return lastComponent(fullPath);\n  }\n}\n\nexport function getMappings(): Mappings {\n  if (mappings_) {\n    return mappings_;\n  }\n  const mappings: Mappings = [];\n  mappings.push(new Mapping<string>('bucket'));\n  mappings.push(new Mapping<string>('generation'));\n  mappings.push(new Mapping<string>('metageneration'));\n  mappings.push(new Mapping<string>('name', 'fullPath', true));\n\n  function mappingsXformPath(\n    _metadata: Metadata,\n    fullPath: string | undefined\n  ): string | undefined {\n    return xformPath(fullPath);\n  }\n  const nameMapping = new Mapping<string>('name');\n  nameMapping.xform = mappingsXformPath;\n  mappings.push(nameMapping);\n\n  /**\n   * Coerces the second param to a number, if it is defined.\n   */\n  function xformSize(\n    _metadata: Metadata,\n    size?: number | string\n  ): number | undefined {\n    if (size !== undefined) {\n      return Number(size);\n    } else {\n      return size;\n    }\n  }\n  const sizeMapping = new Mapping<number>('size');\n  sizeMapping.xform = xformSize;\n  mappings.push(sizeMapping);\n  mappings.push(new Mapping<number>('timeCreated'));\n  mappings.push(new Mapping<string>('updated'));\n  mappings.push(new Mapping<string>('md5Hash', null, true));\n  mappings.push(new Mapping<string>('cacheControl', null, true));\n  mappings.push(new Mapping<string>('contentDisposition', null, true));\n  mappings.push(new Mapping<string>('contentEncoding', null, true));\n  mappings.push(new Mapping<string>('contentLanguage', null, true));\n  mappings.push(new Mapping<string>('contentType', null, true));\n  mappings.push(new Mapping<string>('metadata', 'customMetadata', true));\n  mappings_ = mappings;\n  return mappings_;\n}\n\nexport function addRef(metadata: Metadata, service: FirebaseStorageImpl): void {\n  function generateRef(): Reference {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const loc = new Location(bucket, path);\n    return service._makeStorageReference(loc);\n  }\n  Object.defineProperty(metadata, 'ref', { get: generateRef });\n}\n\nexport function fromResource(\n  service: FirebaseStorageImpl,\n  resource: { [name: string]: unknown },\n  mappings: Mappings\n): Metadata {\n  const metadata: Metadata = {} as Metadata;\n  metadata['type'] = 'file';\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    metadata[mapping.local] = (mapping as Mapping<unknown>).xform(\n      metadata,\n      resource[mapping.server]\n    );\n  }\n  addRef(metadata, service);\n  return metadata;\n}\n\nexport function fromResourceString(\n  service: FirebaseStorageImpl,\n  resourceString: string,\n  mappings: Mappings\n): Metadata | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as Metadata;\n  return fromResource(service, resource, mappings);\n}\n\nexport function downloadUrlFromResourceString(\n  metadata: Metadata,\n  resourceString: string,\n  host: string,\n  protocol: string\n): string | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  if (!isString(obj['downloadTokens'])) {\n    // This can happen if objects are uploaded through GCS and retrieved\n    // through list, so we don't want to throw an Error.\n    return null;\n  }\n  const tokens: string = obj['downloadTokens'] as string;\n  if (tokens.length === 0) {\n    return null;\n  }\n  const encode = encodeURIComponent;\n  const tokensList = tokens.split(',');\n  const urls = tokensList.map((token: string): string => {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n    const base = makeUrl(urlPart, host, protocol);\n    const queryString = makeQueryString({\n      alt: 'media',\n      token\n    });\n    return base + queryString;\n  });\n  return urls[0];\n}\n\nexport function toResourceString(\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): string {\n  const resource: {\n    [prop: string]: unknown;\n  } = {};\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    if (mapping.writable) {\n      resource[mapping.server] = metadata[mapping.local];\n    }\n  }\n  return JSON.stringify(resource);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the listOptions and listResult format\n */\nimport { Location } from './location';\nimport { jsonObjectOrNull } from './json';\nimport { ListResult } from '../list';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Represents the simplified object metadata returned by List API.\n * Other fields are filtered because list in Firebase Rules does not grant\n * the permission to read the metadata.\n */\ninterface ListMetadataResponse {\n  name: string;\n  bucket: string;\n}\n\n/**\n * Represents the JSON response of List API.\n */\ninterface ListResultResponse {\n  prefixes: string[];\n  items: ListMetadataResponse[];\n  nextPageToken?: string;\n}\n\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\n\nfunction fromBackendResponse(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resource: ListResultResponse\n): ListResult {\n  const listResult: ListResult = {\n    prefixes: [],\n    items: [],\n    nextPageToken: resource['nextPageToken']\n  };\n  if (resource[PREFIXES_KEY]) {\n    for (const path of resource[PREFIXES_KEY]) {\n      const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n      const reference = service._makeStorageReference(\n        new Location(bucket, pathWithoutTrailingSlash)\n      );\n      listResult.prefixes.push(reference);\n    }\n  }\n\n  if (resource[ITEMS_KEY]) {\n    for (const item of resource[ITEMS_KEY]) {\n      const reference = service._makeStorageReference(\n        new Location(bucket, item['name'])\n      );\n      listResult.items.push(reference);\n    }\n  }\n  return listResult;\n}\n\nexport function fromResponseString(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resourceString: string\n): ListResult | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as unknown as ListResultResponse;\n  return fromBackendResponse(service, bucket, resource);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Headers, Connection, ConnectionType } from './connection';\n\n/**\n * Type for url params stored in RequestInfo.\n */\nexport interface UrlParams {\n  [name: string]: string | number;\n}\n\n/**\n * A function that converts a server response to the API type expected by the\n * SDK.\n *\n * @param I - the type of the backend's network response\n * @param O - the output response type used by the rest of the SDK.\n */\nexport type RequestHandler<I extends ConnectionType, O> = (\n  connection: Connection<I>,\n  response: I\n) => O;\n\n/** A function to handle an error. */\nexport type ErrorHandler = (\n  connection: Connection<ConnectionType>,\n  response: StorageError\n) => StorageError;\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nexport class RequestInfo<I extends ConnectionType, O> {\n  urlParams: UrlParams = {};\n  headers: Headers = {};\n  body: Blob | string | Uint8Array | null = null;\n  errorHandler: ErrorHandler | null = null;\n\n  /**\n   * Called with the current number of bytes uploaded and total size (-1 if not\n   * computable) of the request body (i.e. used to report upload progress).\n   */\n  progressCallback: ((p1: number, p2: number) => void) | null = null;\n  successCodes: number[] = [200];\n  additionalRetryCodes: number[] = [];\n\n  constructor(\n    public url: string,\n    public method: string,\n    /**\n     * Returns the value with which to resolve the request's promise. Only called\n     * if the request is successful. Throw from this function to reject the\n     * returned Request's promise with the thrown error.\n     * Note: The XhrIo passed to this function may be reused after this callback\n     * returns. Do not keep a reference to it in any way.\n     */\n    public handler: RequestHandler<I, O>,\n    public timeout: number\n  ) {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods for interacting with the network.\n */\n\nimport { Metadata } from '../metadata';\nimport { ListResult } from '../list';\nimport { FbsBlob } from './blob';\nimport {\n  StorageError,\n  cannotSliceBlob,\n  unauthenticated,\n  quotaExceeded,\n  unauthorized,\n  objectNotFound,\n  serverFileWrongSize,\n  unknown,\n  unauthorizedApp\n} from './error';\nimport { Location } from './location';\nimport {\n  Mappings,\n  fromResourceString,\n  downloadUrlFromResourceString,\n  toResourceString\n} from './metadata';\nimport { fromResponseString } from './list';\nimport { RequestInfo, UrlParams } from './requestinfo';\nimport { isString } from './type';\nimport { makeUrl } from './url';\nimport { Connection, ConnectionType } from './connection';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nexport function handlerCheck(cndn: boolean): void {\n  if (!cndn) {\n    throw unknown();\n  }\n}\n\nexport function metadataHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => Metadata {\n  function handler(xhr: Connection<string>, text: string): Metadata {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return metadata as Metadata;\n  }\n  return handler;\n}\n\nexport function listHandler(\n  service: FirebaseStorageImpl,\n  bucket: string\n): (p1: Connection<string>, p2: string) => ListResult {\n  function handler(xhr: Connection<string>, text: string): ListResult {\n    const listResult = fromResponseString(service, bucket, text);\n    handlerCheck(listResult !== null);\n    return listResult as ListResult;\n  }\n  return handler;\n}\n\nexport function downloadUrlHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => string | null {\n  function handler(xhr: Connection<string>, text: string): string | null {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return downloadUrlFromResourceString(\n      metadata as Metadata,\n      text,\n      service.host,\n      service._protocol\n    );\n  }\n  return handler;\n}\n\nexport function sharedErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr: StorageError;\n    if (xhr.getStatus() === 401) {\n      if (\n        // This exact message string is the only consistent part of the\n        // server's error response that identifies it as an App Check error.\n        xhr.getErrorText().includes('Firebase App Check token is invalid')\n      ) {\n        newErr = unauthorizedApp();\n      } else {\n        newErr = unauthenticated();\n      }\n    } else {\n      if (xhr.getStatus() === 402) {\n        newErr = quotaExceeded(location.bucket);\n      } else {\n        if (xhr.getStatus() === 403) {\n          newErr = unauthorized(location.path);\n        } else {\n          newErr = err;\n        }\n      }\n    }\n    newErr.status = xhr.getStatus();\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function objectErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  const shared = sharedErrorHandler(location);\n\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr = shared(xhr, err);\n    if (xhr.getStatus() === 404) {\n      newErr = objectNotFound(location.path);\n    }\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function getMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function list(\n  service: FirebaseStorageImpl,\n  location: Location,\n  delimiter?: string,\n  pageToken?: string | null,\n  maxResults?: number | null\n): RequestInfo<string, ListResult> {\n  const urlParams: UrlParams = {};\n  if (location.isRoot) {\n    urlParams['prefix'] = '';\n  } else {\n    urlParams['prefix'] = location.path + '/';\n  }\n  if (delimiter && delimiter.length > 0) {\n    urlParams['delimiter'] = delimiter;\n  }\n  if (pageToken) {\n    urlParams['pageToken'] = pageToken;\n  }\n  if (maxResults) {\n    urlParams['maxResults'] = maxResults;\n  }\n  const urlPart = location.bucketOnlyServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    listHandler(service, location.bucket),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\nexport function getBytes<I extends ConnectionType>(\n  service: FirebaseStorageImpl,\n  location: Location,\n  maxDownloadSizeBytes?: number\n): RequestInfo<I, I> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    (_: Connection<I>, data: I) => data,\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  if (maxDownloadSizeBytes !== undefined) {\n    requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n    requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n  }\n  return requestInfo;\n}\n\nexport function getDownloadUrl(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, string | null> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    downloadUrlHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function updateMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'PATCH';\n  const body = toResourceString(metadata, mappings);\n  const headers = { 'Content-Type': 'application/json; charset=utf-8' };\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function deleteObject(\n  service: FirebaseStorageImpl,\n  location: Location\n): RequestInfo<string, void> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'DELETE';\n  const timeout = service.maxOperationRetryTime;\n\n  function handler(_xhr: Connection<string>, _text: string): void {}\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.successCodes = [200, 204];\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function determineContentType_(\n  metadata: Metadata | null,\n  blob: FbsBlob | null\n): string {\n  return (\n    (metadata && metadata['contentType']) ||\n    (blob && blob.type()) ||\n    'application/octet-stream'\n  );\n}\n\nexport function metadataForUpload_(\n  location: Location,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): Metadata {\n  const metadataClone = Object.assign({}, metadata);\n  metadataClone['fullPath'] = location.path;\n  metadataClone['size'] = blob.size();\n  if (!metadataClone['contentType']) {\n    metadataClone['contentType'] = determineContentType_(null, blob);\n  }\n  return metadataClone;\n}\n\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nexport function multipartUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, Metadata> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const headers: { [prop: string]: string } = {\n    'X-Goog-Upload-Protocol': 'multipart'\n  };\n\n  function genBoundary(): string {\n    let str = '';\n    for (let i = 0; i < 2; i++) {\n      str = str + Math.random().toString().slice(2);\n    }\n    return str;\n  }\n  const boundary = genBoundary();\n  headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n  const metadata_ = metadataForUpload_(location, blob, metadata);\n  const metadataString = toResourceString(metadata_, mappings);\n  const preBlobPart =\n    '--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' +\n    metadataString +\n    '\\r\\n--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: ' +\n    metadata_['contentType'] +\n    '\\r\\n\\r\\n';\n  const postBlobPart = '\\r\\n--' + boundary + '--';\n  const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  const urlParams: UrlParams = { name: metadata_['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nexport class ResumableUploadStatus {\n  finalized: boolean;\n  metadata: Metadata | null;\n\n  constructor(\n    public current: number,\n    public total: number,\n    finalized?: boolean,\n    metadata?: Metadata | null\n  ) {\n    this.finalized = !!finalized;\n    this.metadata = metadata || null;\n  }\n}\n\nexport function checkResumeHeader_(\n  xhr: Connection<string>,\n  allowed?: string[]\n): string {\n  let status: string | null = null;\n  try {\n    status = xhr.getResponseHeader('X-Goog-Upload-Status');\n  } catch (e) {\n    handlerCheck(false);\n  }\n  const allowedStatus = allowed || ['active'];\n  handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n  return status as string;\n}\n\nexport function createResumableUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, string> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const metadataForUpload = metadataForUpload_(location, blob, metadata);\n  const urlParams: UrlParams = { name: metadataForUpload['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const headers = {\n    'X-Goog-Upload-Protocol': 'resumable',\n    'X-Goog-Upload-Command': 'start',\n    'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n    'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType']!,\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const body = toResourceString(metadataForUpload, mappings);\n  const timeout = service.maxUploadRetryTime;\n\n  function handler(xhr: Connection<string>): string {\n    checkResumeHeader_(xhr);\n    let url;\n    try {\n      url = xhr.getResponseHeader('X-Goog-Upload-URL');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    handlerCheck(isString(url));\n    return url as string;\n  }\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nexport function getResumableUploadStatus(\n  service: FirebaseStorageImpl,\n  location: Location,\n  url: string,\n  blob: FbsBlob\n): RequestInfo<string, ResumableUploadStatus> {\n  const headers = { 'X-Goog-Upload-Command': 'query' };\n\n  function handler(xhr: Connection<string>): ResumableUploadStatus {\n    const status = checkResumeHeader_(xhr, ['active', 'final']);\n    let sizeString: string | null = null;\n    try {\n      sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n    } catch (e) {\n      handlerCheck(false);\n    }\n\n    if (!sizeString) {\n      // null or empty string\n      handlerCheck(false);\n    }\n\n    const size = Number(sizeString);\n    handlerCheck(!isNaN(size));\n    return new ResumableUploadStatus(size, blob.size(), status === 'final');\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nexport const RESUMABLE_UPLOAD_CHUNK_SIZE: number = 256 * 1024;\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nexport function continueResumableUpload(\n  location: Location,\n  service: FirebaseStorageImpl,\n  url: string,\n  blob: FbsBlob,\n  chunkSize: number,\n  mappings: Mappings,\n  status?: ResumableUploadStatus | null,\n  progressCallback?: ((p1: number, p2: number) => void) | null\n): RequestInfo<string, ResumableUploadStatus> {\n  // TODO(andysoto): standardize on internal asserts\n  // assert(!(opt_status && opt_status.finalized));\n  const status_ = new ResumableUploadStatus(0, 0);\n  if (status) {\n    status_.current = status.current;\n    status_.total = status.total;\n  } else {\n    status_.current = 0;\n    status_.total = blob.size();\n  }\n  if (blob.size() !== status_.total) {\n    throw serverFileWrongSize();\n  }\n  const bytesLeft = status_.total - status_.current;\n  let bytesToUpload = bytesLeft;\n  if (chunkSize > 0) {\n    bytesToUpload = Math.min(bytesToUpload, chunkSize);\n  }\n  const startByte = status_.current;\n  const endByte = startByte + bytesToUpload;\n  let uploadCommand = '';\n  if (bytesToUpload === 0) {\n    uploadCommand = 'finalize';\n  } else if (bytesLeft === bytesToUpload) {\n    uploadCommand = 'upload, finalize';\n  } else {\n    uploadCommand = 'upload';\n  }\n  const headers = {\n    'X-Goog-Upload-Command': uploadCommand,\n    'X-Goog-Upload-Offset': `${status_.current}`\n  };\n  const body = blob.slice(startByte, endByte);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n\n  function handler(\n    xhr: Connection<string>,\n    text: string\n  ): ResumableUploadStatus {\n    // TODO(andysoto): Verify the MD5 of each uploaded range:\n    // the 'x-range-md5' header comes back with status code 308 responses.\n    // We'll only be able to bail out though, because you can't re-upload a\n    // range that you previously uploaded.\n    const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n    const newCurrent = status_.current + bytesToUpload;\n    const size = blob.size();\n    let metadata;\n    if (uploadStatus === 'final') {\n      metadata = metadataHandler(service, mappings)(xhr, text);\n    } else {\n      metadata = null;\n    }\n    return new ResumableUploadStatus(\n      newCurrent,\n      size,\n      uploadStatus === 'final',\n      metadata\n    );\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.progressCallback = progressCallback || null;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Enumerations used for upload tasks.\n */\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport type TaskEvent = string;\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport const TaskEvent = {\n  /**\n   * For this event,\n   * <ul>\n   *   <li>The `next` function is triggered on progress updates and when the\n   *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n   *       argument.</li>\n   *   <li>The `error` function is triggered if the upload is canceled or fails\n   *       for another reason.</li>\n   *   <li>The `complete` function is triggered if the upload completes\n   *       successfully.</li>\n   * </ul>\n   */\n  STATE_CHANGED: 'state_changed'\n};\n\n/**\n * Internal enum for task state.\n */\nexport const enum InternalTaskState {\n  RUNNING = 'running',\n  PAUSING = 'pausing',\n  PAUSED = 'paused',\n  SUCCESS = 'success',\n  CANCELING = 'canceling',\n  CANCELED = 'canceled',\n  ERROR = 'error'\n}\n\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport type TaskState = (typeof TaskState)[keyof typeof TaskState];\n\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport const TaskState = {\n  /** The task is currently transferring data. */\n  RUNNING: 'running',\n\n  /** The task was paused by the user. */\n  PAUSED: 'paused',\n\n  /** The task completed successfully. */\n  SUCCESS: 'success',\n\n  /** The task was canceled. */\n  CANCELED: 'canceled',\n\n  /** The task failed with an error. */\n  ERROR: 'error'\n} as const;\n\nexport function taskStateFromInternalTaskState(\n  state: InternalTaskState\n): TaskState {\n  switch (state) {\n    case InternalTaskState.RUNNING:\n    case InternalTaskState.PAUSING:\n    case InternalTaskState.CANCELING:\n      return TaskState.RUNNING;\n    case InternalTaskState.PAUSED:\n      return TaskState.PAUSED;\n    case InternalTaskState.SUCCESS:\n      return TaskState.SUCCESS;\n    case InternalTaskState.CANCELED:\n      return TaskState.CANCELED;\n    case InternalTaskState.ERROR:\n      return TaskState.ERROR;\n    default:\n      // TODO(andysoto): assert(false);\n      return TaskState.ERROR;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isFunction } from './type';\nimport { StorageError } from './error';\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `StorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * An observer identical to the `Observer` defined in packages/util except the\n * error passed into the ErrorFn is specifically a `StorageError`.\n */\nexport interface StorageObserver<T> {\n  /**\n   * Function that is called once for each value in the event stream.\n   */\n  next?: NextFn<T>;\n  /**\n   * A function that is called with a `StorageError`\n   * if the event stream ends due to an error.\n   */\n  error?: ErrorFn;\n  /**\n   * A function that is called if the event stream ends normally.\n   */\n  complete?: CompleteFn;\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\nexport class Observer<T> implements StorageObserver<T> {\n  next?: NextFn<T>;\n  error?: ErrorFn;\n  complete?: CompleteFn;\n\n  constructor(\n    nextOrObserver?: NextFn<T> | StorageObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ) {\n    const asFunctions =\n      isFunction(nextOrObserver) || error != null || complete != null;\n    if (asFunctions) {\n      this.next = nextOrObserver as NextFn<T>;\n      this.error = error ?? undefined;\n      this.complete = complete ?? undefined;\n    } else {\n      const observer = nextOrObserver as {\n        next?: NextFn<T>;\n        error?: ErrorFn;\n        complete?: CompleteFn;\n      };\n      this.next = observer.next;\n      this.error = observer.error;\n      this.complete = observer.complete;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(f: Function): Function {\n  return (...argsToForward: unknown[]) => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.resolve().then(() => f(...argsToForward));\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { isCloudWorkstation } from '@firebase/util';\nimport {\n  Connection,\n  ConnectionType,\n  ErrorCode,\n  Headers\n} from '../../implementation/connection';\nimport { internalError } from '../../implementation/error';\n\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride: (() => Connection<string>) | null = null;\n\n/**\n * Network layer for browsers. We use this instead of goog.net.XhrIo because\n * goog.net.XhrIo is hyuuuuge and doesn't work in React Native on Android.\n */\nabstract class XhrConnection<T extends ConnectionType>\n  implements Connection<T>\n{\n  protected xhr_: XMLHttpRequest;\n  private errorCode_: ErrorCode;\n  private sendPromise_: Promise<void>;\n  protected sent_: boolean = false;\n\n  constructor() {\n    this.xhr_ = new XMLHttpRequest();\n    this.initXhr();\n    this.errorCode_ = ErrorCode.NO_ERROR;\n    this.sendPromise_ = new Promise(resolve => {\n      this.xhr_.addEventListener('abort', () => {\n        this.errorCode_ = ErrorCode.ABORT;\n        resolve();\n      });\n      this.xhr_.addEventListener('error', () => {\n        this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        resolve();\n      });\n      this.xhr_.addEventListener('load', () => {\n        resolve();\n      });\n    });\n  }\n\n  abstract initXhr(): void;\n\n  send(\n    url: string,\n    method: string,\n    isUsingEmulator: boolean,\n    body?: ArrayBufferView | Blob | string,\n    headers?: Headers\n  ): Promise<void> {\n    if (this.sent_) {\n      throw internalError('cannot .send() more than once');\n    }\n    if (isCloudWorkstation(url) && isUsingEmulator) {\n      this.xhr_.withCredentials = true;\n    }\n    this.sent_ = true;\n    this.xhr_.open(method, url, true);\n    if (headers !== undefined) {\n      for (const key in headers) {\n        if (headers.hasOwnProperty(key)) {\n          this.xhr_.setRequestHeader(key, headers[key].toString());\n        }\n      }\n    }\n    if (body !== undefined) {\n      this.xhr_.send(body);\n    } else {\n      this.xhr_.send();\n    }\n    return this.sendPromise_;\n  }\n\n  getErrorCode(): ErrorCode {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorCode() before sending');\n    }\n    return this.errorCode_;\n  }\n\n  getStatus(): number {\n    if (!this.sent_) {\n      throw internalError('cannot .getStatus() before sending');\n    }\n    try {\n      return this.xhr_.status;\n    } catch (e) {\n      return -1;\n    }\n  }\n\n  getResponse(): T {\n    if (!this.sent_) {\n      throw internalError('cannot .getResponse() before sending');\n    }\n    return this.xhr_.response;\n  }\n\n  getErrorText(): string {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorText() before sending');\n    }\n    return this.xhr_.statusText;\n  }\n\n  /** Aborts the request. */\n  abort(): void {\n    this.xhr_.abort();\n  }\n\n  getResponseHeader(header: string): string | null {\n    return this.xhr_.getResponseHeader(header);\n  }\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.addEventListener('progress', listener);\n    }\n  }\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.removeEventListener('progress', listener);\n    }\n  }\n}\n\nexport class XhrTextConnection extends XhrConnection<string> {\n  initXhr(): void {\n    this.xhr_.responseType = 'text';\n  }\n}\n\nexport function newTextConnection(): Connection<string> {\n  return textFactoryOverride ? textFactoryOverride() : new XhrTextConnection();\n}\n\nexport class XhrBytesConnection extends XhrConnection<ArrayBuffer> {\n  private data_?: ArrayBuffer;\n\n  initXhr(): void {\n    this.xhr_.responseType = 'arraybuffer';\n  }\n}\n\nexport function newBytesConnection(): Connection<ArrayBuffer> {\n  return new XhrBytesConnection();\n}\n\nexport class XhrBlobConnection extends XhrConnection<Blob> {\n  initXhr(): void {\n    this.xhr_.responseType = 'blob';\n  }\n}\n\nexport function newBlobConnection(): Connection<Blob> {\n  return new XhrBlobConnection();\n}\n\nexport function newStreamConnection(): Connection<ReadableStream> {\n  throw new Error('Streams are only supported on Node');\n}\n\nexport function injectTestConnection(\n  factory: (() => Connection<string>) | null\n): void {\n  textFactoryOverride = factory;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Defines types for interacting with blob transfer tasks.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport {\n  canceled,\n  StorageErrorCode,\n  StorageError,\n  retryLimitExceeded\n} from './implementation/error';\nimport {\n  InternalTaskState,\n  TaskEvent,\n  TaskState,\n  taskStateFromInternalTaskState\n} from './implementation/taskenums';\nimport { Metadata } from './metadata';\nimport {\n  Observer,\n  Subscribe,\n  Unsubscribe,\n  StorageObserver as StorageObserverInternal,\n  NextFn\n} from './implementation/observer';\nimport { Request } from './implementation/request';\nimport { UploadTaskSnapshot, StorageObserver } from './public-types';\nimport { async as fbsAsync } from './implementation/async';\nimport { Mappings, getMappings } from './implementation/metadata';\nimport {\n  createResumableUpload,\n  getResumableUploadStatus,\n  RESUMABLE_UPLOAD_CHUNK_SIZE,\n  ResumableUploadStatus,\n  continueResumableUpload,\n  getMetadata,\n  multipartUpload\n} from './implementation/requests';\nimport { Reference } from './reference';\nimport { newTextConnection } from './platform/connection';\nimport { isRetryStatusCode } from './implementation/utils';\nimport { CompleteFn } from '@firebase/util';\nimport { DEFAULT_MIN_SLEEP_TIME_MILLIS } from './implementation/constants';\n\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nexport class UploadTask {\n  private _ref: Reference;\n  /**\n   * The data to be uploaded.\n   */\n  _blob: FbsBlob;\n  /**\n   * Metadata related to the upload.\n   */\n  _metadata: Metadata | null;\n  private _mappings: Mappings;\n  /**\n   * Number of bytes transferred so far.\n   */\n  _transferred: number = 0;\n  private _needToFetchStatus: boolean = false;\n  private _needToFetchMetadata: boolean = false;\n  private _observers: Array<StorageObserverInternal<UploadTaskSnapshot>> = [];\n  private _resumable: boolean;\n  /**\n   * Upload state.\n   */\n  _state: InternalTaskState;\n  private _error?: StorageError = undefined;\n  private _uploadUrl?: string = undefined;\n  private _request?: Request<unknown> = undefined;\n  private _chunkMultiplier: number = 1;\n  private _errorHandler: (p1: StorageError) => void;\n  private _metadataErrorHandler: (p1: StorageError) => void;\n  private _resolve?: (p1: UploadTaskSnapshot) => void = undefined;\n  private _reject?: (p1: StorageError) => void = undefined;\n  private pendingTimeout?: ReturnType<typeof setTimeout>;\n  private _promise: Promise<UploadTaskSnapshot>;\n\n  private sleepTime: number;\n\n  private maxSleepTime: number;\n\n  isExponentialBackoffExpired(): boolean {\n    return this.sleepTime > this.maxSleepTime;\n  }\n\n  /**\n   * @param ref - The firebaseStorage.Reference object this task came\n   *     from, untyped to avoid cyclic dependencies.\n   * @param blob - The blob to upload.\n   */\n  constructor(ref: Reference, blob: FbsBlob, metadata: Metadata | null = null) {\n    this._ref = ref;\n    this._blob = blob;\n    this._metadata = metadata;\n    this._mappings = getMappings();\n    this._resumable = this._shouldDoResumable(this._blob);\n    this._state = InternalTaskState.RUNNING;\n    this._errorHandler = error => {\n      this._request = undefined;\n      this._chunkMultiplier = 1;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this._needToFetchStatus = true;\n        this.completeTransitions_();\n      } else {\n        const backoffExpired = this.isExponentialBackoffExpired();\n        if (isRetryStatusCode(error.status, [])) {\n          if (backoffExpired) {\n            error = retryLimitExceeded();\n          } else {\n            this.sleepTime = Math.max(\n              this.sleepTime * 2,\n              DEFAULT_MIN_SLEEP_TIME_MILLIS\n            );\n            this._needToFetchStatus = true;\n            this.completeTransitions_();\n            return;\n          }\n        }\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this._metadataErrorHandler = error => {\n      this._request = undefined;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this.completeTransitions_();\n      } else {\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this.sleepTime = 0;\n    this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n      this._start();\n    });\n\n    // Prevent uncaught rejections on the internal promise from bubbling out\n    // to the top level with a dummy handler.\n    this._promise.then(null, () => {});\n  }\n\n  private _makeProgressCallback(): (p1: number, p2: number) => void {\n    const sizeBefore = this._transferred;\n    return loaded => this._updateProgress(sizeBefore + loaded);\n  }\n\n  private _shouldDoResumable(blob: FbsBlob): boolean {\n    return blob.size() > 256 * 1024;\n  }\n\n  private _start(): void {\n    if (this._state !== InternalTaskState.RUNNING) {\n      // This can happen if someone pauses us in a resume callback, for example.\n      return;\n    }\n    if (this._request !== undefined) {\n      return;\n    }\n    if (this._resumable) {\n      if (this._uploadUrl === undefined) {\n        this._createResumable();\n      } else {\n        if (this._needToFetchStatus) {\n          this._fetchStatus();\n        } else {\n          if (this._needToFetchMetadata) {\n            // Happens if we miss the metadata on upload completion.\n            this._fetchMetadata();\n          } else {\n            this.pendingTimeout = setTimeout(() => {\n              this.pendingTimeout = undefined;\n              this._continueUpload();\n            }, this.sleepTime);\n          }\n        }\n      }\n    } else {\n      this._oneShotUpload();\n    }\n  }\n\n  private _resolveToken(\n    callback: (authToken: string | null, appCheckToken: string | null) => void\n  ): void {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.all([\n      this._ref.storage._getAuthToken(),\n      this._ref.storage._getAppCheckToken()\n    ]).then(([authToken, appCheckToken]) => {\n      switch (this._state) {\n        case InternalTaskState.RUNNING:\n          callback(authToken, appCheckToken);\n          break;\n        case InternalTaskState.CANCELING:\n          this._transition(InternalTaskState.CANCELED);\n          break;\n        case InternalTaskState.PAUSING:\n          this._transition(InternalTaskState.PAUSED);\n          break;\n        default:\n      }\n    });\n  }\n\n  // TODO(andysoto): assert false\n\n  private _createResumable(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = createResumableUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const createRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = createRequest;\n      createRequest.getPromise().then((url: string) => {\n        this._request = undefined;\n        this._uploadUrl = url;\n        this._needToFetchStatus = false;\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _fetchStatus(): void {\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getResumableUploadStatus(\n        this._ref.storage,\n        this._ref._location,\n        url,\n        this._blob\n      );\n      const statusRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = statusRequest;\n      statusRequest.getPromise().then(status => {\n        status = status as ResumableUploadStatus;\n        this._request = undefined;\n        this._updateProgress(status.current);\n        this._needToFetchStatus = false;\n        if (status.finalized) {\n          this._needToFetchMetadata = true;\n        }\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _continueUpload(): void {\n    const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    const status = new ResumableUploadStatus(\n      this._transferred,\n      this._blob.size()\n    );\n\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      let requestInfo;\n      try {\n        requestInfo = continueResumableUpload(\n          this._ref._location,\n          this._ref.storage,\n          url,\n          this._blob,\n          chunkSize,\n          this._mappings,\n          status,\n          this._makeProgressCallback()\n        );\n      } catch (e) {\n        this._error = e as StorageError;\n        this._transition(InternalTaskState.ERROR);\n        return;\n      }\n      const uploadRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken,\n        /*retry=*/ false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n      );\n      this._request = uploadRequest;\n      uploadRequest.getPromise().then((newStatus: ResumableUploadStatus) => {\n        this._increaseMultiplier();\n        this._request = undefined;\n        this._updateProgress(newStatus.current);\n        if (newStatus.finalized) {\n          this._metadata = newStatus.metadata;\n          this._transition(InternalTaskState.SUCCESS);\n        } else {\n          this.completeTransitions_();\n        }\n      }, this._errorHandler);\n    });\n  }\n\n  private _increaseMultiplier(): void {\n    const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n\n    // Max chunk size is 32M.\n    if (currentSize * 2 < 32 * 1024 * 1024) {\n      this._chunkMultiplier *= 2;\n    }\n  }\n\n  private _fetchMetadata(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getMetadata(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings\n      );\n      const metadataRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = metadataRequest;\n      metadataRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._metadataErrorHandler);\n    });\n  }\n\n  private _oneShotUpload(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = multipartUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const multipartRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = multipartRequest;\n      multipartRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._updateProgress(this._blob.size());\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._errorHandler);\n    });\n  }\n\n  private _updateProgress(transferred: number): void {\n    const old = this._transferred;\n    this._transferred = transferred;\n\n    // A progress update can make the \"transferred\" value smaller (e.g. a\n    // partial upload not completed by server, after which the \"transferred\"\n    // value may reset to the value at the beginning of the request).\n    if (this._transferred !== old) {\n      this._notifyObservers();\n    }\n  }\n\n  private _transition(state: InternalTaskState): void {\n    if (this._state === state) {\n      return;\n    }\n    switch (state) {\n      case InternalTaskState.CANCELING:\n      case InternalTaskState.PAUSING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        if (this._request !== undefined) {\n          this._request.cancel();\n        } else if (this.pendingTimeout) {\n          clearTimeout(this.pendingTimeout);\n          this.pendingTimeout = undefined;\n          this.completeTransitions_();\n        }\n        break;\n      case InternalTaskState.RUNNING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        const wasPaused = this._state === InternalTaskState.PAUSED;\n        this._state = state;\n        if (wasPaused) {\n          this._notifyObservers();\n          this._start();\n        }\n        break;\n      case InternalTaskState.PAUSED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.CANCELED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._error = canceled();\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.ERROR:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.SUCCESS:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      default: // Ignore\n    }\n  }\n\n  private completeTransitions_(): void {\n    switch (this._state) {\n      case InternalTaskState.PAUSING:\n        this._transition(InternalTaskState.PAUSED);\n        break;\n      case InternalTaskState.CANCELING:\n        this._transition(InternalTaskState.CANCELED);\n        break;\n      case InternalTaskState.RUNNING:\n        this._start();\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        break;\n    }\n  }\n\n  /**\n   * A snapshot of the current task state.\n   */\n  get snapshot(): UploadTaskSnapshot {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    return {\n      bytesTransferred: this._transferred,\n      totalBytes: this._blob.size(),\n      state: externalState,\n      metadata: this._metadata!,\n      task: this,\n      ref: this._ref\n    };\n  }\n\n  /**\n   * Adds a callback for an event.\n   * @param type - The type of event to listen for.\n   * @param nextOrObserver -\n   *     The `next` function, which gets called for each item in\n   *     the event stream, or an observer object with some or all of these three\n   *     properties (`next`, `error`, `complete`).\n   * @param error - A function that gets called with a `StorageError`\n   *     if the event stream ends due to an error.\n   * @param completed - A function that gets called if the\n   *     event stream ends normally.\n   * @returns\n   *     If only the event argument is passed, returns a function you can use to\n   *     add callbacks (see the examples above). If more than just the event\n   *     argument is passed, returns a function you can call to unregister the\n   *     callbacks.\n   */\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | StorageObserver<UploadTaskSnapshot>\n      | null\n      | ((snapshot: UploadTaskSnapshot) => unknown),\n    error?: ((a: StorageError) => unknown) | null,\n    completed?: CompleteFn | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshot> {\n    // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n    const observer = new Observer(\n      (nextOrObserver as\n        | StorageObserverInternal<UploadTaskSnapshot>\n        | NextFn<UploadTaskSnapshot>) || undefined,\n      error || undefined,\n      completed || undefined\n    );\n    this._addObserver(observer);\n    return () => {\n      this._removeObserver(observer);\n    };\n  }\n\n  /**\n   * This object behaves like a Promise, and resolves with its snapshot data\n   * when the upload completes.\n   * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n   * @param onRejected - The rejection callback.\n   */\n  then<U>(\n    onFulfilled?: ((value: UploadTaskSnapshot) => U | Promise<U>) | null,\n    onRejected?: ((error: StorageError) => U | Promise<U>) | null\n  ): Promise<U> {\n    // These casts are needed so that TypeScript can infer the types of the\n    // resulting Promise.\n    return this._promise.then<U>(\n      onFulfilled as (value: UploadTaskSnapshot) => U | Promise<U>,\n      onRejected as ((error: unknown) => Promise<never>) | null\n    );\n  }\n\n  /**\n   * Equivalent to calling `then(null, onRejected)`.\n   */\n  catch<T>(onRejected: (p1: StorageError) => T | Promise<T>): Promise<T> {\n    return this.then(null, onRejected);\n  }\n\n  /**\n   * Adds the given observer.\n   */\n  private _addObserver(observer: Observer<UploadTaskSnapshot>): void {\n    this._observers.push(observer);\n    this._notifyObserver(observer);\n  }\n\n  /**\n   * Removes the given observer.\n   */\n  private _removeObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const i = this._observers.indexOf(observer);\n    if (i !== -1) {\n      this._observers.splice(i, 1);\n    }\n  }\n\n  private _notifyObservers(): void {\n    this._finishPromise();\n    const observers = this._observers.slice();\n    observers.forEach(observer => {\n      this._notifyObserver(observer);\n    });\n  }\n\n  private _finishPromise(): void {\n    if (this._resolve !== undefined) {\n      let triggered = true;\n      switch (taskStateFromInternalTaskState(this._state)) {\n        case TaskState.SUCCESS:\n          fbsAsync(this._resolve.bind(null, this.snapshot))();\n          break;\n        case TaskState.CANCELED:\n        case TaskState.ERROR:\n          const toCall = this._reject as (p1: StorageError) => void;\n          fbsAsync(toCall.bind(null, this._error as StorageError))();\n          break;\n        default:\n          triggered = false;\n          break;\n      }\n      if (triggered) {\n        this._resolve = undefined;\n        this._reject = undefined;\n      }\n    }\n  }\n\n  private _notifyObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    switch (externalState) {\n      case TaskState.RUNNING:\n      case TaskState.PAUSED:\n        if (observer.next) {\n          fbsAsync(observer.next.bind(observer, this.snapshot))();\n        }\n        break;\n      case TaskState.SUCCESS:\n        if (observer.complete) {\n          fbsAsync(observer.complete.bind(observer))();\n        }\n        break;\n      case TaskState.CANCELED:\n      case TaskState.ERROR:\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n    }\n  }\n\n  /**\n   * Resumes a paused task. Has no effect on a currently running or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  resume(): boolean {\n    const valid =\n      this._state === InternalTaskState.PAUSED ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.RUNNING);\n    }\n    return valid;\n  }\n\n  /**\n   * Pauses a currently running task. Has no effect on a paused or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  pause(): boolean {\n    const valid = this._state === InternalTaskState.RUNNING;\n    if (valid) {\n      this._transition(InternalTaskState.PAUSING);\n    }\n    return valid;\n  }\n\n  /**\n   * Cancels a currently running or paused task. Has no effect on a complete or\n   * failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  cancel(): boolean {\n    const valid =\n      this._state === InternalTaskState.RUNNING ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.CANCELING);\n    }\n    return valid;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines the Firebase StorageReference class.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport { Location } from './implementation/location';\nimport { getMappings } from './implementation/metadata';\nimport { child, lastComponent, parent } from './implementation/path';\nimport {\n  deleteObject as requestsDeleteObject,\n  getBytes,\n  getDownloadUrl as requestsGetDownloadUrl,\n  getMetadata as requestsGetMetadata,\n  list as requestsList,\n  multipartUpload,\n  updateMetadata as requestsUpdateMetadata\n} from './implementation/requests';\nimport { ListOptions, UploadResult } from './public-types';\nimport { dataFromString, StringFormat } from './implementation/string';\nimport { Metadata } from './metadata';\nimport { FirebaseStorageImpl } from './service';\nimport { ListResult } from './list';\nimport { UploadTask } from './task';\nimport { invalidRootOperation, noDownloadURL } from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport {\n  newBlobConnection,\n  newBytesConnection,\n  newStreamConnection,\n  newTextConnection\n} from './platform/connection';\nimport { RequestInfo } from './implementation/requestinfo';\n\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nexport class Reference {\n  _location: Location;\n\n  constructor(\n    private _service: FirebaseStorageImpl,\n    location: string | Location\n  ) {\n    if (location instanceof Location) {\n      this._location = location;\n    } else {\n      this._location = Location.makeFromUrl(location, _service.host);\n    }\n  }\n\n  /**\n   * Returns the URL for the bucket and path this object references,\n   *     in the form gs://<bucket>/<object-path>\n   * @override\n   */\n  toString(): string {\n    return 'gs://' + this._location.bucket + '/' + this._location.path;\n  }\n\n  protected _newRef(\n    service: FirebaseStorageImpl,\n    location: Location\n  ): Reference {\n    return new Reference(service, location);\n  }\n\n  /**\n   * A reference to the root of this object's bucket.\n   */\n  get root(): Reference {\n    const location = new Location(this._location.bucket, '');\n    return this._newRef(this._service, location);\n  }\n\n  /**\n   * The name of the bucket containing this reference's object.\n   */\n  get bucket(): string {\n    return this._location.bucket;\n  }\n\n  /**\n   * The full path of this object.\n   */\n  get fullPath(): string {\n    return this._location.path;\n  }\n\n  /**\n   * The short name of this object, which is the last component of the full path.\n   * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n   */\n  get name(): string {\n    return lastComponent(this._location.path);\n  }\n\n  /**\n   * The `StorageService` instance this `StorageReference` is associated with.\n   */\n  get storage(): FirebaseStorageImpl {\n    return this._service;\n  }\n\n  /**\n   * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n   * this reference is the root.\n   */\n  get parent(): Reference | null {\n    const newPath = parent(this._location.path);\n    if (newPath === null) {\n      return null;\n    }\n    const location = new Location(this._location.bucket, newPath);\n    return new Reference(this._service, location);\n  }\n\n  /**\n   * Utility function to throw an error in methods that do not accept a root reference.\n   */\n  _throwIfRoot(name: string): void {\n    if (this._location.path === '') {\n      throw invalidRootOperation(name);\n    }\n  }\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nexport function getBytesInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref._throwIfRoot('getBytes');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBytesConnection)\n    .then(bytes =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (bytes as ArrayBuffer).slice(0, maxDownloadSizeBytes)\n        : (bytes as ArrayBuffer)\n    );\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded blob.\n */\nexport function getBlobInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<Blob> {\n  ref._throwIfRoot('getBlob');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBlobConnection)\n    .then(blob =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (blob as Blob).slice(0, maxDownloadSizeBytes)\n        : (blob as Blob)\n    );\n}\n\n/** Stream the bytes at the object's location. */\nexport function getStreamInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): ReadableStream {\n  ref._throwIfRoot('getStream');\n  const requestInfo: RequestInfo<ReadableStream, ReadableStream> = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n\n  // Transforms the stream so that only `maxDownloadSizeBytes` bytes are piped to the result\n  const newMaxSizeTransform = (n: number): Transformer => {\n    let missingBytes = n;\n    return {\n      transform(chunk, controller: TransformStreamDefaultController) {\n        // GCS may not honor the Range header for small files\n        if (chunk.length < missingBytes) {\n          controller.enqueue(chunk);\n          missingBytes -= chunk.length;\n        } else {\n          controller.enqueue(chunk.slice(0, missingBytes));\n          controller.terminate();\n        }\n      }\n    };\n  };\n\n  const result =\n    maxDownloadSizeBytes !== undefined\n      ? new TransformStream(newMaxSizeTransform(maxDownloadSizeBytes))\n      : new TransformStream(); // The default transformer forwards all chunks to its readable side\n\n  ref.storage\n    .makeRequestWithTokens(requestInfo, newStreamConnection)\n    .then(readableStream => readableStream.pipeThrough(result))\n    .catch(err => result.writable.abort(err));\n\n  return result.readable;\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadBytes');\n  const requestInfo = multipartUpload(\n    ref.storage,\n    ref._location,\n    getMappings(),\n    new FbsBlob(data, true),\n    metadata\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(finalMetadata => {\n      return {\n        metadata: finalMetadata,\n        ref\n      };\n    });\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): UploadTask {\n  ref._throwIfRoot('uploadBytesResumable');\n  return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: Reference,\n  value: string,\n  format: StringFormat = StringFormat.RAW,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadString');\n  const data = dataFromString(format, value);\n  const metadataClone = { ...metadata } as Metadata;\n  if (metadataClone['contentType'] == null && data.contentType != null) {\n    metadataClone['contentType'] = data.contentType!;\n  }\n  return uploadBytes(ref, data.data, metadataClone);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: Reference): Promise<ListResult> {\n  const accumulator: ListResult = {\n    prefixes: [],\n    items: []\n  };\n  return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(\n  ref: Reference,\n  accumulator: ListResult,\n  pageToken?: string\n): Promise<void> {\n  const opt: ListOptions = {\n    // maxResults is 1000 by default.\n    pageToken\n  };\n  const nextPage = await list(ref, opt);\n  accumulator.prefixes.push(...nextPage.prefixes);\n  accumulator.items.push(...nextPage.items);\n  if (nextPage.nextPageToken != null) {\n    await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n  }\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: Reference,\n  options?: ListOptions | null\n): Promise<ListResult> {\n  if (options != null) {\n    if (typeof options.maxResults === 'number') {\n      validateNumber(\n        'options.maxResults',\n        /* minValue= */ 1,\n        /* maxValue= */ 1000,\n        options.maxResults\n      );\n    }\n  }\n  const op = options || {};\n  const requestInfo = requestsList(\n    ref.storage,\n    ref._location,\n    /*delimiter= */ '/',\n    op.pageToken,\n    op.maxResults\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nexport function getMetadata(ref: Reference): Promise<Metadata> {\n  ref._throwIfRoot('getMetadata');\n  const requestInfo = requestsGetMetadata(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nexport function updateMetadata(\n  ref: Reference,\n  metadata: Partial<Metadata>\n): Promise<Metadata> {\n  ref._throwIfRoot('updateMetadata');\n  const requestInfo = requestsUpdateMetadata(\n    ref.storage,\n    ref._location,\n    metadata,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: Reference): Promise<string> {\n  ref._throwIfRoot('getDownloadURL');\n  const requestInfo = requestsGetDownloadUrl(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(url => {\n      if (url === null) {\n        throw noDownloadURL();\n      }\n      return url;\n    });\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: Reference): Promise<void> {\n  ref._throwIfRoot('deleteObject');\n  const requestInfo = requestsDeleteObject(ref.storage, ref._location);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nexport function _getChild(ref: Reference, childPath: string): Reference {\n  const newPath = child(ref._location.path, childPath);\n  const location = new Location(ref._location.bucket, newPath);\n  return new Reference(ref.storage, location);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Location } from './implementation/location';\nimport { FailRequest } from './implementation/failrequest';\nimport { Request, makeRequest } from './implementation/request';\nimport { RequestInfo } from './implementation/requestinfo';\nimport { Reference, _getChild } from './reference';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  _isFirebaseServerApp\n} from '@firebase/app';\nimport {\n  CONFIG_STORAGE_BUCKET_KEY,\n  DEFAULT_HOST,\n  DEFAULT_MAX_OPERATION_RETRY_TIME,\n  DEFAULT_MAX_UPLOAD_RETRY_TIME\n} from './implementation/constants';\nimport {\n  invalidArgument,\n  appDeleted,\n  noDefaultBucket\n} from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport { FirebaseStorage } from './public-types';\nimport {\n  createMockUserToken,\n  EmulatorMockTokenOptions,\n  isCloudWorkstation,\n  pingServer,\n  updateEmulatorBanner\n} from '@firebase/util';\nimport { Connection, ConnectionType } from './implementation/connection';\n\nexport function isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service: FirebaseStorageImpl, url: string): Reference {\n  return new Reference(service, url);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(\n  ref: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference {\n  if (ref instanceof FirebaseStorageImpl) {\n    const service = ref;\n    if (service._bucket == null) {\n      throw noDefaultBucket();\n    }\n    const reference = new Reference(service, service._bucket!);\n    if (path != null) {\n      return refFromPath(reference, path);\n    } else {\n      return reference;\n    }\n  } else {\n    // ref is a Reference\n    if (path !== undefined) {\n      return _getChild(ref, path);\n    } else {\n      return ref;\n    }\n  }\n}\n\n/**\n * Returns a storage Reference for the given url.\n * @param storage - `Storage` instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorageImpl, url?: string): Reference;\n/**\n * Returns a storage Reference for the given path in the\n * default bucket.\n * @param storageOrRef - `Storage` service or storage `Reference`.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if Storage\n * instance provided) or returns same reference (if Reference provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference;\nexport function ref(\n  serviceOrRef: FirebaseStorageImpl | Reference,\n  pathOrUrl?: string\n): Reference | null {\n  if (pathOrUrl && isUrl(pathOrUrl)) {\n    if (serviceOrRef instanceof FirebaseStorageImpl) {\n      return refFromURL(serviceOrRef, pathOrUrl);\n    } else {\n      throw invalidArgument(\n        'To use ref(service, url), the first argument must be a Storage instance.'\n      );\n    }\n  } else {\n    return refFromPath(serviceOrRef, pathOrUrl);\n  }\n}\n\nfunction extractBucket(\n  host: string,\n  config?: FirebaseOptions\n): Location | null {\n  const bucketString = config?.[CONFIG_STORAGE_BUCKET_KEY];\n  if (bucketString == null) {\n    return null;\n  }\n  return Location.makeFromBucketSpec(bucketString, host);\n}\n\nexport function connectStorageEmulator(\n  storage: FirebaseStorageImpl,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  storage.host = `${host}:${port}`;\n  const useSsl = isCloudWorkstation(host);\n  // Workaround to get cookies in Firebase Studio\n  if (useSsl) {\n    void pingServer(`https://${storage.host}/b`);\n    updateEmulatorBanner('Storage', true);\n  }\n  storage._isUsingEmulator = true;\n  storage._protocol = useSsl ? 'https' : 'http';\n  const { mockUserToken } = options;\n  if (mockUserToken) {\n    storage._overrideAuthToken =\n      typeof mockUserToken === 'string'\n        ? mockUserToken\n        : createMockUserToken(mockUserToken, storage.app.options.projectId);\n  }\n}\n\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nexport class FirebaseStorageImpl implements FirebaseStorage {\n  _bucket: Location | null = null;\n  /**\n   * This string can be in the formats:\n   * - host\n   * - host:port\n   */\n  private _host: string = DEFAULT_HOST;\n  _protocol: string = 'https';\n  protected readonly _appId: string | null = null;\n  private readonly _requests: Set<Request<unknown>>;\n  private _deleted: boolean = false;\n  private _maxOperationRetryTime: number;\n  private _maxUploadRetryTime: number;\n  _overrideAuthToken?: string;\n\n  constructor(\n    /**\n     * FirebaseApp associated with this StorageService instance.\n     */\n    readonly app: FirebaseApp,\n    readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    /**\n     * @internal\n     */\n    readonly _appCheckProvider: Provider<AppCheckInternalComponentName>,\n    /**\n     * @internal\n     */\n    readonly _url?: string,\n    readonly _firebaseVersion?: string,\n    public _isUsingEmulator = false\n  ) {\n    this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n    this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n    this._requests = new Set();\n    if (_url != null) {\n      this._bucket = Location.makeFromBucketSpec(_url, this._host);\n    } else {\n      this._bucket = extractBucket(this._host, this.app.options);\n    }\n  }\n\n  /**\n   * The host string for this service, in the form of `host` or\n   * `host:port`.\n   */\n  get host(): string {\n    return this._host;\n  }\n\n  set host(host: string) {\n    this._host = host;\n    if (this._url != null) {\n      this._bucket = Location.makeFromBucketSpec(this._url, host);\n    } else {\n      this._bucket = extractBucket(host, this.app.options);\n    }\n  }\n\n  /**\n   * The maximum time to retry uploads in milliseconds.\n   */\n  get maxUploadRetryTime(): number {\n    return this._maxUploadRetryTime;\n  }\n\n  set maxUploadRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxUploadRetryTime = time;\n  }\n\n  /**\n   * The maximum time to retry operations other than uploads or downloads in\n   * milliseconds.\n   */\n  get maxOperationRetryTime(): number {\n    return this._maxOperationRetryTime;\n  }\n\n  set maxOperationRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxOperationRetryTime = time;\n  }\n\n  async _getAuthToken(): Promise<string | null> {\n    if (this._overrideAuthToken) {\n      return this._overrideAuthToken;\n    }\n    const auth = this._authProvider.getImmediate({ optional: true });\n    if (auth) {\n      const tokenData = await auth.getToken();\n      if (tokenData !== null) {\n        return tokenData.accessToken;\n      }\n    }\n    return null;\n  }\n\n  async _getAppCheckToken(): Promise<string | null> {\n    if (_isFirebaseServerApp(this.app) && this.app.settings.appCheckToken) {\n      return this.app.settings.appCheckToken;\n    }\n    const appCheck = this._appCheckProvider.getImmediate({ optional: true });\n    if (appCheck) {\n      const result = await appCheck.getToken();\n      // TODO: What do we want to do if there is an error getting the token?\n      // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n      // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n      // the token (actual or dummy) to send requests.\n      return result.token;\n    }\n    return null;\n  }\n\n  /**\n   * Stop running requests and prevent more from being created.\n   */\n  _delete(): Promise<void> {\n    if (!this._deleted) {\n      this._deleted = true;\n      this._requests.forEach(request => request.cancel());\n      this._requests.clear();\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Returns a new firebaseStorage.Reference object referencing this StorageService\n   * at the given Location.\n   */\n  _makeStorageReference(loc: Location): Reference {\n    return new Reference(this, loc);\n  }\n\n  /**\n   * @param requestInfo - HTTP RequestInfo object\n   * @param authToken - Firebase auth token\n   */\n  _makeRequest<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>,\n    authToken: string | null,\n    appCheckToken: string | null,\n    retry = true\n  ): Request<O> {\n    if (!this._deleted) {\n      const request = makeRequest(\n        requestInfo,\n        this._appId,\n        authToken,\n        appCheckToken,\n        requestFactory,\n        this._firebaseVersion,\n        retry,\n        this._isUsingEmulator\n      );\n      this._requests.add(request);\n      // Request removes itself from set when complete.\n      request.getPromise().then(\n        () => this._requests.delete(request),\n        () => this._requests.delete(request)\n      );\n      return request;\n    } else {\n      return new FailRequest(appDeleted());\n    }\n  }\n\n  async makeRequestWithTokens<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>\n  ): Promise<O> {\n    const [authToken, appCheckToken] = await Promise.all([\n      this._getAuthToken(),\n      this._getAppCheckToken()\n    ]);\n\n    return this._makeRequest(\n      requestInfo,\n      requestFactory,\n      authToken,\n      appCheckToken\n    ).getPromise();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\n\nimport {\n  ref as refInternal,\n  FirebaseStorageImpl,\n  connectStorageEmulator as connectEmulatorInternal\n} from './service';\nimport { Provider } from '@firebase/component';\n\nimport {\n  StorageReference,\n  FirebaseStorage,\n  UploadResult,\n  ListOptions,\n  ListResult,\n  UploadTask,\n  SettableMetadata,\n  UploadMetadata,\n  FullMetadata\n} from './public-types';\nimport { Metadata as MetadataInternal } from './metadata';\nimport {\n  uploadBytes as uploadBytesInternal,\n  uploadBytesResumable as uploadBytesResumableInternal,\n  uploadString as uploadStringInternal,\n  getMetadata as getMetadataInternal,\n  updateMetadata as updateMetadataInternal,\n  list as listInternal,\n  listAll as listAllInternal,\n  getDownloadURL as getDownloadURLInternal,\n  deleteObject as deleteObjectInternal,\n  Reference,\n  _getChild as _getChildInternal,\n  getBytesInternal\n} from './reference';\nimport { STORAGE_TYPE } from './constants';\nimport {\n  EmulatorMockTokenOptions,\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\nimport { StringFormat } from './implementation/string';\n\nexport { EmulatorMockTokenOptions } from '@firebase/util';\n\nexport { StorageError, StorageErrorCode } from './implementation/error';\n\n/**\n * Public types.\n */\nexport * from './public-types';\n\nexport { Location as _Location } from './implementation/location';\nexport { UploadTask as _UploadTask } from './task';\nexport type { Reference as _Reference } from './reference';\nexport type { FirebaseStorageImpl as _FirebaseStorageImpl } from './service';\nexport { FbsBlob as _FbsBlob } from './implementation/blob';\nexport { dataFromString as _dataFromString } from './implementation/string';\nexport {\n  invalidRootOperation as _invalidRootOperation,\n  invalidArgument as _invalidArgument\n} from './implementation/error';\nexport {\n  TaskEvent as _TaskEvent,\n  TaskState as _TaskState\n} from './implementation/taskenums';\nexport { StringFormat };\n\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nexport function getBytes(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref = getModularInstance(ref);\n  return getBytesInternal(ref as Reference, maxDownloadSizeBytes);\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadBytesInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: StorageReference,\n  value: string,\n  format?: StringFormat,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadStringInternal(\n    ref as Reference,\n    value,\n    format,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): UploadTask {\n  ref = getModularInstance(ref);\n  return uploadBytesResumableInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  ) as UploadTask;\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nexport function getMetadata(ref: StorageReference): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return getMetadataInternal(ref as Reference) as Promise<FullMetadata>;\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nexport function updateMetadata(\n  ref: StorageReference,\n  metadata: SettableMetadata\n): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return updateMetadataInternal(\n    ref as Reference,\n    metadata as Partial<MetadataInternal>\n  ) as Promise<FullMetadata>;\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: StorageReference,\n  options?: ListOptions\n): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listInternal(ref as Reference, options);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: StorageReference): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listAllInternal(ref as Reference);\n}\n\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: StorageReference): Promise<string> {\n  ref = getModularInstance(ref);\n  return getDownloadURLInternal(ref as Reference);\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: StorageReference): Promise<void> {\n  ref = getModularInstance(ref);\n  return deleteObjectInternal(ref as Reference);\n}\n\n/**\n * Returns a {@link StorageReference} for the given url.\n * @param storage - {@link FirebaseStorage} instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorage, url?: string): StorageReference;\n/**\n * Returns a {@link StorageReference} for the given path in the\n * default bucket.\n * @param storageOrRef - {@link FirebaseStorage} or {@link StorageReference}.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if {@link FirebaseStorage}\n * instance provided) or returns same reference (if {@link StorageReference} provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorage | StorageReference,\n  path?: string\n): StorageReference;\nexport function ref(\n  serviceOrRef: FirebaseStorage | StorageReference,\n  pathOrUrl?: string\n): StorageReference | null {\n  serviceOrRef = getModularInstance(serviceOrRef);\n  return refInternal(\n    serviceOrRef as FirebaseStorageImpl | Reference,\n    pathOrUrl\n  );\n}\n\n/**\n * @internal\n */\nexport function _getChild(ref: StorageReference, childPath: string): Reference {\n  return _getChildInternal(ref as Reference, childPath);\n}\n\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nexport function getStorage(\n  app: FirebaseApp = getApp(),\n  bucketUrl?: string\n): FirebaseStorage {\n  app = getModularInstance(app);\n  const storageProvider: Provider<'storage'> = _getProvider(app, STORAGE_TYPE);\n  const storageInstance = storageProvider.getImmediate({\n    identifier: bucketUrl\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('storage');\n  if (emulator) {\n    connectStorageEmulator(storageInstance, ...emulator);\n  }\n  return storageInstance;\n}\n\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nexport function connectStorageEmulator(\n  storage: FirebaseStorage,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  connectEmulatorInternal(storage as FirebaseStorageImpl, host, port, options);\n}\n", "/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\n\nimport { FirebaseStorageImpl } from '../src/service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\nimport { FirebaseStorage } from './public-types';\nimport { STORAGE_TYPE } from './constants';\n\nexport * from './api';\nexport * from './api.browser';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): FirebaseStorage {\n  const app = container.getProvider('app').getImmediate();\n  const authProvider = container.getProvider('auth-internal');\n  const appCheckProvider = container.getProvider('app-check-internal');\n\n  return new FirebaseStorageImpl(\n    app,\n    authProvider,\n    appCheckProvider,\n    url,\n    SDK_VERSION\n  );\n}\n\nfunction registerStorage(): void {\n  _registerComponent(\n    new Component(\n      STORAGE_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  //RUNTIME_ENV will be replaced during the compilation to \"node\" for nodejs and an empty string for browser\n  registerVersion(name, version, '__RUNTIME_ENV__');\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterStorage();\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Storage.\n */\nexport const STORAGE_TYPE = 'storage';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { UploadTaskSnapshot } from '@firebase/storage';\nimport { ReferenceCompat } from './reference';\nimport { UploadTaskCompat } from './task';\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class UploadTaskSnapshotCompat\n  implements types.UploadTaskSnapshot, Compat<UploadTaskSnapshot>\n{\n  constructor(\n    readonly _delegate: UploadTaskSnapshot,\n    readonly task: UploadTaskCompat,\n    readonly ref: ReferenceCompat\n  ) {}\n\n  get bytesTransferred(): number {\n    return this._delegate.bytesTransferred;\n  }\n  get metadata(): types.FullMetadata {\n    return this._delegate.metadata as types.FullMetadata;\n  }\n  get state(): string {\n    return this._delegate.state;\n  }\n  get totalBytes(): number {\n    return this._delegate.totalBytes;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  UploadTask,\n  StorageError,\n  UploadTaskSnapshot,\n  TaskEvent,\n  StorageObserver\n} from '@firebase/storage';\nimport { UploadTaskSnapshotCompat } from './tasksnapshot';\nimport { ReferenceCompat } from './reference';\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class UploadTaskCompat implements types.UploadTask, Compat<UploadTask> {\n  constructor(\n    readonly _delegate: UploadTask,\n    private readonly _ref: ReferenceCompat\n  ) {}\n\n  get snapshot(): UploadTaskSnapshotCompat {\n    return new UploadTaskSnapshotCompat(\n      this._delegate.snapshot,\n      this,\n      this._ref\n    );\n  }\n\n  cancel = this._delegate.cancel.bind(this._delegate);\n  catch = this._delegate.catch.bind(this._delegate);\n  pause = this._delegate.pause.bind(this._delegate);\n  resume = this._delegate.resume.bind(this._delegate);\n\n  then(\n    onFulfilled?: ((a: UploadTaskSnapshotCompat) => unknown) | null,\n    onRejected?: ((a: StorageError) => unknown) | null\n  ): Promise<unknown> {\n    return this._delegate.then(snapshot => {\n      if (onFulfilled) {\n        return onFulfilled(\n          new UploadTaskSnapshotCompat(snapshot, this, this._ref)\n        );\n      }\n    }, onRejected);\n  }\n\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | types.StorageObserver<UploadTaskSnapshotCompat>\n      | null\n      | ((a: UploadTaskSnapshotCompat) => unknown),\n    error?: ((error: StorageError) => void) | null,\n    completed?: () => void | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshotCompat> {\n    let wrappedNextOrObserver:\n      | StorageObserver<UploadTaskSnapshot>\n      | undefined\n      | ((a: UploadTaskSnapshot) => unknown) = undefined;\n    if (!!nextOrObserver) {\n      if (typeof nextOrObserver === 'function') {\n        wrappedNextOrObserver = (taskSnapshot: UploadTaskSnapshot) =>\n          nextOrObserver(\n            new UploadTaskSnapshotCompat(taskSnapshot, this, this._ref)\n          );\n      } else {\n        wrappedNextOrObserver = {\n          next: !!nextOrObserver.next\n            ? (taskSnapshot: UploadTaskSnapshot) =>\n                nextOrObserver.next!(\n                  new UploadTaskSnapshotCompat(taskSnapshot, this, this._ref)\n                )\n            : undefined,\n          complete: nextOrObserver.complete || undefined,\n          error: nextOrObserver.error || undefined\n        };\n      }\n    }\n    return this._delegate.on(\n      type,\n      wrappedNextOrObserver,\n      error || undefined,\n      completed || undefined\n    );\n  }\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `FirebaseStorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ListResult } from '@firebase/storage';\nimport * as types from '@firebase/storage-types';\nimport { ReferenceCompat } from './reference';\nimport { StorageServiceCompat } from './service';\nimport { Compat } from '@firebase/util';\n\nexport class ListResultCompat implements types.ListResult, Compat<ListResult> {\n  constructor(\n    readonly _delegate: ListResult,\n    private readonly _service: StorageServiceCompat\n  ) {}\n\n  get prefixes(): ReferenceCompat[] {\n    return this._delegate.prefixes.map(\n      ref => new ReferenceCompat(ref, this._service)\n    );\n  }\n  get items(): ReferenceCompat[] {\n    return this._delegate.items.map(\n      ref => new ReferenceCompat(ref, this._service)\n    );\n  }\n  get nextPageToken(): string | null {\n    return this._delegate.nextPageToken || null;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *  http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  StorageReference,\n  uploadBytesResumable,\n  list,\n  listAll,\n  getDownloadURL,\n  getMetadata,\n  updateMetadata,\n  deleteObject,\n  UploadTask,\n  StringFormat,\n  UploadMetadata,\n  FullMetadata,\n  SettableMetadata,\n  _UploadTask,\n  _getChild,\n  _Reference,\n  _FbsBlob,\n  _dataFromString,\n  _invalidRootOperation\n} from '@firebase/storage';\n\nimport { UploadTaskCompat } from './task';\nimport { ListResultCompat } from './list';\nimport { StorageServiceCompat } from './service';\n\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class ReferenceCompat\n  implements types.Reference, Compat<StorageReference>\n{\n  constructor(\n    readonly _delegate: StorageReference,\n    public storage: StorageServiceCompat\n  ) {}\n\n  get name(): string {\n    return this._delegate.name;\n  }\n\n  get bucket(): string {\n    return this._delegate.bucket;\n  }\n\n  get fullPath(): string {\n    return this._delegate.fullPath;\n  }\n\n  toString(): string {\n    return this._delegate.toString();\n  }\n\n  /**\n   * @returns A reference to the object obtained by\n   * appending childPath, removing any duplicate, beginning, or trailing\n   * slashes.\n   */\n  child(childPath: string): types.Reference {\n    const reference = _getChild(this._delegate, childPath);\n    return new ReferenceCompat(reference, this.storage);\n  }\n\n  get root(): types.Reference {\n    return new ReferenceCompat(this._delegate.root, this.storage);\n  }\n\n  /**\n   * @returns A reference to the parent of the\n   * current object, or null if the current object is the root.\n   */\n  get parent(): types.Reference | null {\n    const reference = this._delegate.parent;\n    if (reference == null) {\n      return null;\n    }\n    return new ReferenceCompat(reference, this.storage);\n  }\n\n  /**\n   * Uploads a blob to this object's location.\n   * @param data - The blob to upload.\n   * @returns An UploadTask that lets you control and\n   * observe the upload.\n   */\n  put(\n    data: Blob | Uint8Array | ArrayBuffer,\n    metadata?: types.FullMetadata\n  ): types.UploadTask {\n    this._throwIfRoot('put');\n    return new UploadTaskCompat(\n      uploadBytesResumable(this._delegate, data, metadata as UploadMetadata),\n      this\n    );\n  }\n\n  /**\n   * Uploads a string to this object's location.\n   * @param value - The string to upload.\n   * @param format - The format of the string to upload.\n   * @returns An UploadTask that lets you control and\n   * observe the upload.\n   */\n  putString(\n    value: string,\n    format: StringFormat = StringFormat.RAW,\n    metadata?: types.UploadMetadata\n  ): types.UploadTask {\n    this._throwIfRoot('putString');\n    const data = _dataFromString(format, value);\n    const metadataClone = { ...metadata };\n    if (metadataClone['contentType'] == null && data.contentType != null) {\n      metadataClone['contentType'] = data.contentType;\n    }\n    return new UploadTaskCompat(\n      new _UploadTask(\n        this._delegate as _Reference,\n        new _FbsBlob(data.data, true),\n        metadataClone as FullMetadata & { [k: string]: string }\n      ) as UploadTask,\n      this\n    );\n  }\n\n  /**\n   * List all items (files) and prefixes (folders) under this storage reference.\n   *\n   * This is a helper method for calling list() repeatedly until there are\n   * no more results. The default pagination size is 1000.\n   *\n   * Note: The results may not be consistent if objects are changed while this\n   * operation is running.\n   *\n   * Warning: listAll may potentially consume too many resources if there are\n   * too many results.\n   *\n   * @returns A Promise that resolves with all the items and prefixes under\n   *  the current storage reference. `prefixes` contains references to\n   *  sub-directories and `items` contains references to objects in this\n   *  folder. `nextPageToken` is never returned.\n   */\n  listAll(): Promise<types.ListResult> {\n    return listAll(this._delegate).then(\n      r => new ListResultCompat(r, this.storage)\n    );\n  }\n\n  /**\n   * List items (files) and prefixes (folders) under this storage reference.\n   *\n   * List API is only available for Firebase Rules Version 2.\n   *\n   * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n   * delimited folder structure. Refer to GCS's List API if you want to learn more.\n   *\n   * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n   * support objects whose paths end with \"/\" or contain two consecutive\n   * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n   * list() may fail if there are too many unsupported objects in the bucket.\n   *\n   * @param options - See ListOptions for details.\n   * @returns A Promise that resolves with the items and prefixes.\n   * `prefixes` contains references to sub-folders and `items`\n   * contains references to objects in this folder. `nextPageToken`\n   * can be used to get the rest of the results.\n   */\n  list(options?: types.ListOptions | null): Promise<types.ListResult> {\n    return list(this._delegate, options || undefined).then(\n      r => new ListResultCompat(r, this.storage)\n    );\n  }\n\n  /**\n   * A `Promise` that resolves with the metadata for this object. If this\n   * object doesn't exist or metadata cannot be retrieved, the promise is\n   * rejected.\n   */\n  getMetadata(): Promise<types.FullMetadata> {\n    return getMetadata(this._delegate) as Promise<types.FullMetadata>;\n  }\n\n  /**\n   * Updates the metadata for this object.\n   * @param metadata - The new metadata for the object.\n   * Only values that have been explicitly set will be changed. Explicitly\n   * setting a value to null will remove the metadata.\n   * @returns A `Promise` that resolves\n   * with the new metadata for this object.\n   * @see firebaseStorage.Reference.prototype.getMetadata\n   */\n  updateMetadata(\n    metadata: types.SettableMetadata\n  ): Promise<types.FullMetadata> {\n    return updateMetadata(\n      this._delegate,\n      metadata as SettableMetadata\n    ) as Promise<types.FullMetadata>;\n  }\n\n  /**\n   * @returns A `Promise` that resolves with the download\n   * URL for this object.\n   */\n  getDownloadURL(): Promise<string> {\n    return getDownloadURL(this._delegate);\n  }\n\n  /**\n   * Deletes the object at this location.\n   * @returns A `Promise` that resolves if the deletion succeeds.\n   */\n  delete(): Promise<void> {\n    this._throwIfRoot('delete');\n    return deleteObject(this._delegate);\n  }\n\n  private _throwIfRoot(name: string): void {\n    if ((this._delegate as _Reference)._location.path === '') {\n      throw _invalidRootOperation(name);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as types from '@firebase/storage-types';\nimport { FirebaseApp } from '@firebase/app-types';\n\nimport {\n  ref,\n  connectStorageEmulator,\n  FirebaseStorage,\n  _Location,\n  _invalidArgument,\n  _FirebaseStorageImpl\n} from '@firebase/storage';\nimport { ReferenceCompat } from './reference';\nimport { Compat, EmulatorMockTokenOptions } from '@firebase/util';\n\n/**\n * A service that provides firebaseStorage.Reference instances.\n * @param opt_url gs:// url to a custom Storage Bucket\n */\nexport class StorageServiceCompat\n  implements types.FirebaseStorage, Compat<FirebaseStorage>\n{\n  constructor(public app: FirebaseApp, readonly _delegate: FirebaseStorage) {}\n\n  get maxOperationRetryTime(): number {\n    return this._delegate.maxOperationRetryTime;\n  }\n\n  get maxUploadRetryTime(): number {\n    return this._delegate.maxUploadRetryTime;\n  }\n\n  /**\n   * Returns a firebaseStorage.Reference for the given path in the default\n   * bucket.\n   */\n  ref(path?: string): types.Reference {\n    if (isUrl(path)) {\n      throw _invalidArgument(\n        'ref() expected a child path but got a URL, use refFromURL instead.'\n      );\n    }\n    return new ReferenceCompat(ref(this._delegate, path), this);\n  }\n\n  /**\n   * Returns a firebaseStorage.Reference object for the given absolute URL,\n   * which must be a gs:// or http[s]:// URL.\n   */\n  refFromURL(url: string): types.Reference {\n    if (!isUrl(url)) {\n      throw _invalidArgument(\n        'refFromURL() expected a full URL but got a child path, use ref() instead.'\n      );\n    }\n    try {\n      _Location.makeFromUrl(url, (this._delegate as _FirebaseStorageImpl).host);\n    } catch (e) {\n      throw _invalidArgument(\n        'refFromUrl() expected a valid full URL but got an invalid one.'\n      );\n    }\n    return new ReferenceCompat(ref(this._delegate, url), this);\n  }\n\n  setMaxUploadRetryTime(time: number): void {\n    this._delegate.maxUploadRetryTime = time;\n  }\n\n  setMaxOperationRetryTime(time: number): void {\n    this._delegate.maxOperationRetryTime = time;\n  }\n\n  useEmulator(\n    host: string,\n    port: number,\n    options: {\n      mockUserToken?: EmulatorMockTokenOptions | string;\n    } = {}\n  ): void {\n    connectStorageEmulator(this._delegate, host, port, options);\n  }\n}\n\nfunction isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n"], "names": ["stringToByteArray", "str", "out", "let", "p", "i", "length", "c", "charCodeAt", "base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "this", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "Error", "init_", "byteToCharMap", "output", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte3", "outByte4", "push", "join", "encodeString", "btoa", "decodeString", "byteArrayToString", "bytes", "decodeStringToByteArray", "pos", "u", "c2", "c3", "c1", "String", "fromCharCode", "charToByteMap", "char<PERSON>t", "byte4", "DecodeBase64StringError", "constructor", "name", "base64Encode", "utf8Bytes", "base64urlEncodeWithoutPadding", "replace", "isCloudWorkstation", "host", "endsWith", "emulatorStatus", "previouslyDismissed", "updateEmulatorBanner", "isRunningEmulator", "window", "document", "location", "bannerId", "showError", "key", "summary", "prod", "emulator", "Object", "keys", "prefixedId", "id", "setupCloseBtn", "closeBtn", "createElement", "style", "cursor", "marginLeft", "fontSize", "innerHTML", "onclick", "element", "getElementById", "remove", "setupDom", "prependIcon", "bannerEl", "banner", "parentDiv", "created", "setAttribute", "firebaseTextId", "firebaseText", "learnMoreId", "learnMoreLink", "prependIconId", "createElementNS", "display", "background", "position", "bottom", "left", "padding", "borderRadius", "alignItems", "innerText", "href", "paddingLeft", "textDecoration", "iconId", "append", "body", "append<PERSON><PERSON><PERSON>", "readyState", "addEventListener", "FirebaseError", "code", "message", "customData", "super", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "PATTERN", "_", "value", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "DEFAULT_HOST", "CONFIG_STORAGE_BUCKET_KEY", "StorageError", "status_", "prependCode", "serverResponse", "_baseMessage", "status", "_codeEquals", "StorageErrorCode", "ErrorCode", "instance", "namespaceExports", "unknown", "UNKNOWN", "retryLimitExceeded", "RETRY_LIMIT_EXCEEDED", "canceled", "CANCELED", "cannotSliceBlob", "CANNOT_SLICE_BLOB", "invalidArgument", "INVALID_ARGUMENT", "appDeleted", "APP_DELETED", "invalidRootOperation", "INVALID_ROOT_OPERATION", "invalidFormat", "format", "INVALID_FORMAT", "internalError", "INTERNAL_ERROR", "Location", "bucket", "path", "path_", "isRoot", "fullServerUrl", "encode", "encodeURIComponent", "bucketOnlyServerUrl", "makeFromBucketSpec", "bucketString", "bucketLocation", "makeFromUrl", "e", "INVALID_DEFAULT_BUCKET", "url", "bucketDomain", "gsRegex", "RegExp", "httpModify", "loc", "decodeURIComponent", "firebaseStorageHost", "firebaseStorageRegExp", "cloudStorageHost", "groups", "regex", "indices", "postModify", "slice", "group", "captures", "exec", "bucketValue", "pathValue", "INVALID_URL", "FailRequest", "error", "promise_", "Promise", "reject", "getPromise", "cancel", "_appDelete", "isString", "isNativeBlob", "isNativeBlobDefined", "Blob", "validateNumber", "argument", "minValue", "maxValue", "makeUrl", "urlPart", "protocol", "origin", "makeQueryString", "params", "nextPart", "query<PERSON>art", "hasOwnProperty", "isRetryStatusCode", "additionalRetryCodes", "isFiveHundredCode", "isExtraRetryCode", "indexOf", "isAdditionalRetryCode", "NetworkRequest", "url_", "method_", "headers_", "body_", "successCodes_", "additionalRetryCodes_", "callback_", "errorCallback_", "timeout_", "progressCallback_", "connectionFactory_", "retry", "isUsingEmulator", "pendingConnection_", "backoffId_", "canceled_", "appDelete_", "resolve", "resolve_", "reject_", "start_", "doTheRequest", "backoff<PERSON>allback", "RequestEndStatus", "connection", "progressListener", "progressEvent", "loaded", "total", "lengthComputable", "addUploadProgressListener", "send", "then", "removeUploadProgressListener", "hitServer", "getErrorCode", "NO_ERROR", "getStatus", "wasCanceled", "ABORT", "successCode", "backoffDone", "requestWentThrough", "wasSuccessCode", "result", "getResponse", "err", "getErrorText", "doRequest", "backoffCompleteCb", "timeout", "waitSeconds", "retryTimeoutId", "globalTimeoutId", "hitTimeout", "cancelState", "triggeredCallback", "triggerCallback", "args", "apply", "callWithDelay", "millis", "setTimeout", "response<PERSON><PERSON>ler", "clearGlobalTimeout", "clearTimeout", "success", "call", "<PERSON><PERSON><PERSON><PERSON>", "Math", "random", "stopped", "stop", "wasTimeout", "appDelete", "abort", "getBlob", "BlobBuilder", "WebKitBlobBuilder", "undefined", "bb", "UNSUPPORTED_ENVIRONMENT", "decodeBase64", "encoded", "StringFormat", "RAW", "BASE64", "BASE64URL", "DATA_URL", "StringData", "contentType", "dataFromString", "stringData", "utf8Bytes_", "base64Bytes_", "dataUrl", "parts", "DataURLParts", "rest", "decoded", "hi", "lo", "b", "Uint8Array", "hasMinus", "hasUnder", "hasPlus", "hasSlash", "includes", "array", "dataURL", "matches", "match", "s", "end", "middle", "substring", "FbsBlob", "elideCopy", "size", "blobType", "data_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "set", "size_", "type_", "startByte", "endByte", "blob", "start", "realBlob", "sliced", "webkitSlice", "mozSlice", "buffer", "blobby", "map", "val", "uint8Arrays", "finalLength", "merged", "for<PERSON>ach", "index", "uploadData", "jsonObjectOrNull", "obj", "JSON", "parse", "lastComponent", "lastIndexOf", "noXform_", "metadata", "Mapping", "server", "local", "writable", "xform", "mappings_", "getMappings", "mappings", "sizeMapping", "nameMapping", "_metadata", "fullPath", "Number", "addRef", "defineProperty", "get", "_makeStorageReference", "fromResourceString", "resourceString", "resource", "len", "mapping", "toResourceString", "stringify", "PREFIXES_KEY", "ITEMS_KEY", "fromResponseString", "listResult", "prefixes", "items", "nextPageToken", "pathWithoutTrailingSlash", "reference", "item", "RequestInfo", "method", "handler", "urlParams", "headers", "<PERSON><PERSON><PERSON><PERSON>", "progressCallback", "successCodes", "handler<PERSON><PERSON><PERSON>", "cndn", "metadataHandler", "xhr", "text", "<PERSON><PERSON><PERSON><PERSON>", "downloadUrlHandler", "downloadUrlFromResourceString", "_protocol", "tokens", "split", "alt", "token", "sharedErrorHandler", "newErr", "UNAUTHORIZED_APP", "UNAUTHENTICATED", "QUOTA_EXCEEDED", "UNAUTHORIZED", "objectErrorHandler", "shared", "OBJECT_NOT_FOUND", "getMetadata", "maxOperationRetryTime", "requestInfo", "metadataForUpload_", "metadataClone", "assign", "multipartUpload", "X-Goog-Upload-Protocol", "boundary", "toString", "metadata_", "preBlobPart", "postBlobPart", "maxUploadRetryTime", "ResumableUploadStatus", "current", "finalized", "checkResumeHeader_", "allowed", "getResponseHeader", "createResumableUpload", "metadataForUpload", "X-Goog-Upload-Command", "X-Goog-Upload-Header-Content-Length", "X-Goog-Upload-Header-Content-Type", "Content-Type", "getResumableUploadStatus", "sizeString", "isNaN", "continueResumableUpload", "chunkSize", "SERVER_FILE_WRONG_SIZE", "bytesLeft", "bytesToUpload", "min", "uploadCommand", "X-Goog-Upload-Offset", "uploadStatus", "newCurrent", "TaskEvent", "STATE_CHANGED", "TaskState", "RUNNING", "PAUSED", "SUCCESS", "ERROR", "taskStateFromInternalTaskState", "state", "Observer", "nextOrObserver", "complete", "observer", "next", "async", "f", "argsToForward", "XhrTextConnection", "sent_", "xhr_", "XMLHttpRequest", "initXhr", "errorCode_", "sendPromise_", "NETWORK_ERROR", "withCredentials", "open", "setRequestHeader", "response", "statusText", "header", "listener", "upload", "removeEventListener", "responseType", "newTextConnection", "UploadTask", "isExponentialBackoffExpired", "sleepTime", "maxSleepTime", "ref", "_transferred", "_needToFetchStatus", "_needToFetchMetadata", "_observers", "_error", "_uploadUrl", "_request", "_chunkMultiplier", "_resolve", "_reject", "_ref", "_blob", "_mappings", "_resumable", "_shouldDoResumable", "_state", "_error<PERSON><PERSON><PERSON>", "completeTransitions_", "backoffExpired", "max", "_transition", "_metadataErrorHandler", "storage", "_promise", "_start", "_makeProgressCallback", "sizeBefore", "_updateProgress", "_createResumable", "_fetchStatus", "_fetchMetadata", "pendingTimeout", "_continueUpload", "_oneShotUpload", "_resolveToken", "all", "_getAuthToken", "_getAppCheckToken", "authToken", "appCheckToken", "_location", "createRequest", "_makeRequest", "statusRequest", "uploadRequest", "_increaseMultiplier", "newStatus", "metadataRequest", "multipartRequest", "transferred", "old", "_notifyObservers", "wasPaused", "snapshot", "externalState", "bytesTransferred", "totalBytes", "task", "on", "completed", "_addObserver", "_removeObserver", "onFulfilled", "onRejected", "catch", "_notifyObserver", "splice", "_finishPromise", "triggered", "fbsAsync", "bind", "resume", "valid", "pause", "Reference", "_service", "_newRef", "root", "parent", "newPath", "_throwIfRoot", "listAll", "accumulator", "listAllHelper", "pageToken", "opt", "nextPage", "await", "list", "options", "maxResults", "delimiter", "op", "makeRequestWithTokens", "updateMetadata", "getDownloadURL", "NO_DOWNLOAD_URL", "deleteObject", "_xhr", "_text", "_get<PERSON><PERSON>d", "child<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "component", "ref<PERSON><PERSON><PERSON><PERSON>", "FirebaseStorageImpl", "_bucket", "NO_DEFAULT_BUCKET", "serviceOrRef", "pathOrUrl", "test", "extractBucket", "config", "connectStorageEmulator", "port", "useSsl", "mockUserToken", "endpoint", "fetch", "credentials", "ok", "_isUsingEmulator", "_overrideAuthToken", "projectId", "uid", "project", "iat", "sub", "user_id", "payload", "iss", "aud", "exp", "auth_time", "firebase", "sign_in_provider", "identities", "alg", "app", "_authProvider", "_appCheckProvider", "_url", "_firebaseVersion", "_host", "_appId", "_deleted", "_maxOperationRetryTime", "_maxUploadRetryTime", "_requests", "Set", "time", "POSITIVE_INFINITY", "auth", "getImmediate", "optional", "tokenData", "getToken", "accessToken", "appCheck", "_isFirebaseServerApp", "settings", "_delete", "request", "clear", "requestFactory", "makeRequest", "appId", "firebaseVersion", "add", "delete", "uploadBytesResumable", "requestsGetMetadata", "refInternal", "factory", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "authProvider", "appCheckProvider", "SDK_VERSION", "_registerComponent", "registerVersion", "version", "UploadTaskSnapshotCompat", "UploadTaskCompat", "wrappedNextOrObserver", "taskSnapshot", "ListResultCompat", "ReferenceCompat", "child", "_getChildInternal", "put", "putString", "_dataFromString", "_UploadTask", "_FbsBlob", "listAllInternal", "r", "listInternal", "updateMetadataInternal", "getDownloadURLInternal", "deleteObjectInternal", "_invalidRootOperation", "StorageServiceCompat", "isUrl", "_invalidArgument", "refFromURL", "_Location", "setMaxUploadRetryTime", "setMaxOperationRetryTime", "useEmulator", "connectEmulatorInternal", "storageExp", "identifier", "Storage", "INTERNAL", "registerComponent"], "mappings": "ibAiBMA,EAAoB,SAAUC,GAElC,IAAMC,EAAgB,GACtBC,IAAIC,EAAI,EACR,IAAKD,IAAIE,EAAI,EAAGA,EAAIJ,EAAIK,OAAQD,CAAC,GAAI,CACnCF,IAAII,EAAIN,EAAIO,WAAWH,CAAC,EACpBE,EAAI,IACNL,EAAIE,CAAC,IAAMG,GACFA,EAAI,KACbL,EAAIE,CAAC,IAAOG,GAAK,EAAK,KAGL,QAAZ,MAAJA,IACDF,EAAI,EAAIJ,EAAIK,QACyB,QAAZ,MAAxBL,EAAIO,WAAWH,EAAI,CAAC,IAGrBE,EAAI,QAAgB,KAAJA,IAAe,KAA6B,KAAtBN,EAAIO,WAAW,EAAEH,CAAC,GACxDH,EAAIE,CAAC,IAAOG,GAAK,GAAM,IACvBL,EAAIE,CAAC,IAAQG,GAAK,GAAM,GAAM,KAI9BL,EAAIE,CAAC,IAAOG,GAAK,GAAM,IACvBL,EAAIE,CAAC,IAAQG,GAAK,EAAK,GAAM,KAC7BL,EAAIE,CAAC,IAAW,GAAJG,EAAU,IAEzB,CACD,OAAOL,CACT,EA6DaO,EAAiB,CAI5BC,eAAgB,KAKhBC,eAAgB,KAMhBC,sBAAuB,KAMvBC,sBAAuB,KAMvBC,kBACE,iEAKFC,mBACE,OAAOC,KAAKF,kBAAoB,KACjC,EAKDG,2BACE,OAAOD,KAAKF,kBAAoB,KACjC,EASDI,mBAAoC,YAAhB,OAAOC,KAW3BC,gBAAgBC,EAA8BC,GAC5C,GAAI,CAACC,MAAMC,QAAQH,CAAK,EACtB,MAAMI,MAAM,+CAA+C,EAG7DT,KAAKU,MAAK,EAEV,IAAMC,EAAgBL,EAClBN,KAAKJ,sBACLI,KAAKN,eAEHkB,EAAS,GAEf,IAAKzB,IAAIE,EAAI,EAAGA,EAAIgB,EAAMf,OAAQD,GAAK,EAAG,CACxC,IAAMwB,EAAQR,EAAMhB,GACdyB,EAAYzB,EAAI,EAAIgB,EAAMf,OAC1ByB,EAAQD,EAAYT,EAAMhB,EAAI,GAAK,EACnC2B,EAAY3B,EAAI,EAAIgB,EAAMf,OAC1B2B,EAAQD,EAAYX,EAAMhB,EAAI,GAAK,EAIzCF,IAAI+B,GAAqB,GAARH,IAAiB,EAAME,GAAS,EAC7CE,EAAmB,GAARF,EAEVD,IACHG,EAAW,GAENL,KACHI,EAAW,IAIfN,EAAOQ,KACLT,EAdeE,GAAS,GAexBF,GAdyB,EAARE,IAAiB,EAAME,GAAS,GAejDJ,EAAcO,GACdP,EAAcQ,EAAS,CAE1B,CAED,OAAOP,EAAOS,KAAK,EAAE,CACtB,EAUDC,aAAajB,EAAeC,GAG1B,OAAIN,KAAKE,oBAAsB,CAACI,EACvBiB,KAAKlB,CAAK,EAEZL,KAAKI,gBAAgBpB,EAAkBqB,CAAK,EAAGC,CAAO,CAC9D,EAUDkB,aAAanB,EAAeC,GAG1B,GAAIN,KAAKE,oBAAsB,CAACI,EAC9B,OAAOH,KAAKE,CAAK,EAEZoB,CAAAA,IA9LyBC,EA8LP1B,KAAK2B,wBAAwBtB,EAAOC,CAAO,EA5LtE,IAAMpB,EAAgB,GACtBC,IAAIyC,EAAM,EACRrC,EAAI,EACN,KAAOqC,EAAMF,EAAMpC,QAAQ,CACzB,IAWQuC,EAMAC,EACAC,EAlBFC,EAAKN,EAAME,CAAG,IAChBI,EAAK,IACP9C,EAAIK,CAAC,IAAM0C,OAAOC,aAAaF,CAAE,EACnB,IAALA,GAAYA,EAAK,KACpBF,EAAKJ,EAAME,CAAG,IACpB1C,EAAIK,CAAC,IAAM0C,OAAOC,cAAoB,GAALF,IAAY,EAAW,GAALF,CAAQ,GAC7C,IAALE,GAAYA,EAAK,KAKpBH,IACI,EAALG,IAAW,IAAa,GAJlBN,EAAME,CAAG,MAIgB,IAAa,GAHtCF,EAAME,CAAG,MAGoC,EAAW,GAFxDF,EAAME,CAAG,KAGlB,MACF1C,EAAIK,CAAC,IAAM0C,OAAOC,aAAa,OAAUL,GAAK,GAAG,EACjD3C,EAAIK,CAAC,IAAM0C,OAAOC,aAAa,OAAc,KAAJL,EAAS,IAE5CC,EAAKJ,EAAME,CAAG,IACdG,EAAKL,EAAME,CAAG,IACpB1C,EAAIK,CAAC,IAAM0C,OAAOC,cACT,GAALF,IAAY,IAAa,GAALF,IAAY,EAAW,GAALC,CAAQ,EAGrD,CACD,OAAO7C,EAAImC,KAAK,EAAE,EAgKTI,MAA8D,CACtE,EAiBDE,wBAAwBtB,EAAeC,GACrCN,KAAKU,MAAK,EAEV,IAAMyB,EAAgB7B,EAClBN,KAAKH,sBACLG,KAAKL,eAEHiB,EAAmB,GAEzB,IAAKzB,IAAIE,EAAI,EAAGA,EAAIgB,EAAMf,QAAU,CAClC,IAAMuB,EAAQsB,EAAc9B,EAAM+B,OAAO/C,CAAC,EAAE,GAGtC0B,EADY1B,EAAIgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,CAAC,GAAK,EAIrD4B,EAHN,EAAE5B,EAEoBgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,CAAC,GAAK,GAIrDgD,EAHN,EAAEhD,EAEoBgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,CAAC,GAAK,GAG3D,GAFA,EAAEA,EAEW,MAATwB,GAA0B,MAATE,GAA0B,MAATE,GAA0B,MAAToB,EACrD,MAAM,IAAIC,EAIZ1B,EAAOQ,KADWP,GAAS,EAAME,GAAS,CACtB,EAEN,KAAVE,IAEFL,EAAOQ,KADYL,GAAS,EAAK,IAASE,GAAS,CAC/B,EAEN,KAAVoB,IAEFzB,EAAOQ,KADYH,GAAS,EAAK,IAAQoB,CACrB,CAGzB,CAED,OAAOzB,CACR,EAODF,QACE,GAAI,CAACV,KAAKN,eAAgB,CACxBM,KAAKN,eAAiB,GACtBM,KAAKL,eAAiB,GACtBK,KAAKJ,sBAAwB,GAC7BI,KAAKH,sBAAwB,GAG7B,IAAKV,IAAIE,EAAI,EAAGA,EAAIW,KAAKD,aAAaT,OAAQD,CAAC,GAC7CW,KAAKN,eAAeL,GAAKW,KAAKD,aAAaqC,OAAO/C,CAAC,EACnDW,KAAKL,eAAeK,KAAKN,eAAeL,IAAMA,EAC9CW,KAAKJ,sBAAsBP,GAAKW,KAAKC,qBAAqBmC,OAAO/C,CAAC,GAClEW,KAAKH,sBAAsBG,KAAKJ,sBAAsBP,IAAMA,IAGnDW,KAAKF,kBAAkBR,SAC9BU,KAAKL,eAAeK,KAAKC,qBAAqBmC,OAAO/C,CAAC,GAAKA,EAC3DW,KAAKH,sBAAsBG,KAAKD,aAAaqC,OAAO/C,CAAC,GAAKA,EAG/D,CACF,CACD,QAKWiD,UAAgC7B,MAA7C8B,kCACWvC,KAAIwC,KAAG,yBACjB,CAAA,CAKM,IAAMC,EAAe,SAAUxD,GACpC,IAAMyD,EAAY1D,EAAkBC,CAAG,EACvC,OAAOQ,EAAOW,gBAAgBsC,EAAW,CAAA,CAAI,CAC/C,EAMaC,EAAgC,SAAU1D,GAErD,OAAOwD,EAAaxD,CAAG,EAAE2D,QAAQ,MAAO,EAAE,CAC5C,ECjVM,SAAUC,EAAmBC,GACjC,OAAOA,EAAKC,SAAS,wBAAwB,CAC/C,CC4HA,IAAMC,EAAoC,GAkC1C7D,IAAI8D,EAAsB,CAAA,EAOV,SAAAC,EACdV,EACAW,GAEA,GACoB,aAAlB,OAAOC,QACa,aAApB,OAAOC,UACNR,EAAmBO,OAAOE,SAASR,IAAI,GACxCE,EAAeR,KAAUW,GACzBH,CAAAA,EAAeR,IACfS,CAAAA,EANF,CAWAD,EAAeR,GAAQW,EAKvB,IAAMI,EAAW,qBAEjB,IAAMC,EAAkC,GAvD1C,KACE,IAIWC,EAJLC,EAA2B,CAC/BC,KAAM,GACNC,SAAU,EACX,EACD,IAAWH,KAAOI,OAAOC,KAAKd,CAAc,GACtCA,EAAeS,GACjBC,EAAQE,SAERF,EAAQC,MAFSvC,KAAKqC,CAAG,EAK7B,OAAOC,CACT,KA0C4BC,KAAKrE,OAL/B,SAASyE,EAAWC,GAClB,MAAO,uBAAuBA,CAC/B,CAgCD,SAASC,IACP,IAAMC,EAAWb,SAASc,cAAc,MAAM,EAS9C,OARAD,EAASE,MAAMC,OAAS,UACxBH,EAASE,MAAME,WAAa,OAC5BJ,EAASE,MAAMG,SAAW,OAC1BL,EAASM,UAAY,WACrBN,EAASO,QAAU,KAjCrB,IACQC,EAiCJzB,EAAsB,CAAA,GAjClByB,EAAUrB,SAASsB,eAAepB,CAAQ,IAE9CmB,EAAQE,OAAM,CAiChB,EACOV,CACR,CAeD,SAASW,IACP,IApCuBC,EAXEC,EA+CnBC,GAhGahB,IACrB7E,IAAI8F,EAAY5B,SAASsB,eAAeX,CAAE,EACtCkB,EAAU,CAAA,EAMd,OALKD,KACHA,EAAY5B,SAASc,cAAc,KAAK,GAC9BgB,aAAa,KAAMnB,CAAE,EAC/BkB,EAAU,CAAA,GAEL,CAAEA,QAAAA,EAASR,QAASO,EAC7B,GAuFiC1B,CAAQ,EAC/B6B,EAAiBrB,EAAW,MAAM,EAClCsB,EACJhC,SAASsB,eAAeS,CAAc,GAAK/B,SAASc,cAAc,MAAM,EACpEmB,EAAcvB,EAAW,WAAW,EACpCwB,EACHlC,SAASsB,eAAeW,CAAW,GACpCjC,SAASc,cAAc,GAAG,EACtBqB,EAAgBzB,EAAW,cAAc,EACzCe,EACHzB,SAASsB,eACRa,CAAa,GAEfnC,SAASoC,gBAAgB,6BAA8B,KAAK,EAC1DT,EAAOE,UAEHH,EAAWC,EAAON,SA/DDK,EAgELA,GA/DXX,MAAMsB,QAAU,OACzBX,EAASX,MAAMuB,WAAa,UAC5BZ,EAASX,MAAMwB,SAAW,QAC1Bb,EAASX,MAAMyB,OAAS,MACxBd,EAASX,MAAM0B,KAAO,MACtBf,EAASX,MAAM2B,QAAU,OACzBhB,EAASX,MAAM4B,aAAe,MAC9BjB,EAASX,MAAM6B,WAAa,UA0B5BV,EA+BkBA,GA5BJJ,aAAa,KA4BMG,CA5BW,EAC5CC,EAAcW,UAAY,aAC1BX,EAAcY,KACZ,uEACFZ,EAAcJ,aAAa,SAAU,SAAS,EAC9CI,EAAcnB,MAAMgC,YAAc,MAClCb,EAAcnB,MAAMiC,eAAiB,YAuB7BnC,EAAWD,IAvD6BqC,EAwDjBd,GAxDRV,EAwDLA,GAvDNK,aAAa,QAAS,IAAI,EACtCL,EAAYK,aAAa,KAAMmB,CAAM,EACrCxB,EAAYK,aAAa,SAAU,IAAI,EACvCL,EAAYK,aAAa,UAAW,WAAW,EAC/CL,EAAYK,aAAa,OAAQ,MAAM,EACvCL,EAAYV,MAAME,WAAa,OAmD7BS,EAASwB,OAAOzB,EAAaO,EAAcE,EAAerB,CAAQ,EAClEb,SAASmD,KAAKC,YAAY1B,CAAQ,GAGhCvB,GACF6B,EAAaa,UAAY,gCACzBpB,EAAYN;;;;;;;WASZM,EAAYN;;;;;;;SAQZa,EAAaa,UAAY,8CAE3Bb,EAAaF,aAAa,KAAMC,CAAc,CAC/C,CAC2B,YAAxB/B,SAASqD,WACXtD,OAAOuD,iBAAiB,mBAAoB9B,CAAQ,EAEpDA,GApHD,CAsHH,OCtPa+B,UAAsBnG,MAIjC8B,YAEWsE,EACTC,EAEOC,GAEPC,MAAMF,CAAO,EALJ9G,KAAI6G,KAAJA,EAGF7G,KAAU+G,WAAVA,EAPA/G,KAAIwC,KAdI,gBA6BfqB,OAAOoD,eAAejH,KAAM4G,EAAcM,SAAS,EAI/CzG,MAAM0G,mBACR1G,MAAM0G,kBAAkBnH,KAAMoH,EAAaF,UAAUG,MAAM,CAE9D,CACF,OAEYD,EAIX7E,YACmB+E,EACAC,EACAC,GAFAxH,KAAOsH,QAAPA,EACAtH,KAAWuH,YAAXA,EACAvH,KAAMwH,OAANA,CACf,CAEJH,OACER,KACGY,GAEH,IAcuCA,EAdjCV,EAAcU,EAAK,IAAoB,GACvCC,EAAc1H,KAAKsH,QAAR,IAAmBT,EAC9Bc,EAAW3H,KAAKwH,OAAOX,GAEvBC,EAAUa,GAUuBF,EAVcV,EAAVY,EAW7B/E,QAAQgF,EAAS,CAACC,EAAGpE,KACnC,IAAMqE,EAAQL,EAAKhE,GACnB,OAAgB,MAATqE,EAAgB7F,OAAO6F,CAAK,MAAQrE,KAC7C,CAAC,GAdoE,QAE7DsE,EAAiB/H,KAAKuH,iBAAgBT,MAAYY,MAIxD,OAFc,IAAId,EAAcc,EAAUK,EAAahB,CAAU,CAGlE,CACF,CASD,IAAMa,EAAU,gBClHV,SAAUI,EACdV,GAEA,OAAIA,GAAYA,EAA+BW,UACrCX,EAA+BW,UAEhCX,CAEX,OCDaY,EAiBX3F,YACWC,EACA2F,EACAC,GAFApI,KAAIwC,KAAJA,EACAxC,KAAemI,gBAAfA,EACAnI,KAAIoI,KAAJA,EAnBXpI,KAAiBqI,kBAAG,CAAA,EAIpBrI,KAAYsI,aAAe,GAE3BtI,KAAAuI,kBAA2C,OAE3CvI,KAAiBwI,kBAAwC,IAYrD,CAEJC,qBAAqBC,GAEnB,OADA1I,KAAKuI,kBAAoBG,EAClB1I,IACR,CAED2I,qBAAqBN,GAEnB,OADArI,KAAKqI,kBAAoBA,EAClBrI,IACR,CAED4I,gBAAgBC,GAEd,OADA7I,KAAKsI,aAAeO,EACb7I,IACR,CAED8I,2BAA2BC,GAEzB,OADA/I,KAAKwI,kBAAoBO,EAClB/I,IACR,CACF,CC/CM,IAAMgJ,EAAe,iCAKfC,EAA4B,sBCH5BC,UAAqBtC,EAahCrE,YAAYsE,EAAwBC,EAAyBqC,EAAU,GACrEnC,MACEoC,EAAYvC,CAAI,uBACKC,MAAYsC,EAAYvC,CAAI,IAAI,EAHI7G,KAAOmJ,QAAPA,EAR7DnJ,KAAA+G,WAAgD,CAAEsC,eAAgB,IAAI,EAapErJ,KAAKsJ,aAAetJ,KAAK8G,QAGzBjD,OAAOoD,eAAejH,KAAMkJ,EAAahC,SAAS,CACnD,CAEDqC,aACE,OAAOvJ,KAAKmJ,OACb,CAEDI,WAAWA,GACTvJ,KAAKmJ,QAAUI,CAChB,CAKDC,YAAY3C,GACV,OAAOuC,EAAYvC,CAAI,IAAM7G,KAAK6G,IACnC,CAKDwC,qBACE,OAAOrJ,KAAK+G,WAAWsC,cACxB,CAEDA,mBAAmBA,GACjBrJ,KAAK+G,WAAWsC,eAAiBA,EAC7BrJ,KAAK+G,WAAWsC,eAClBrJ,KAAK8G,QAAa9G,KAAKsJ;EAAiBtJ,KAAK+G,WAAWsC,eAExDrJ,KAAK8G,QAAU9G,KAAKsJ,YAEvB,CACF,KAQWG,ECfAC,ECZoBC,EACxBC,EFwDF,SAAUR,EAAYvC,GAC1B,MAAO,WAAaA,CACtB,CAEgB,SAAAgD,IAId,OAAO,IAAIX,EAAaO,EAAiBK,QAFvC,gFAEuD,CAC3D,CAsDgB,SAAAC,IACd,OAAO,IAAIb,EACTO,EAAiBO,qBACjB,0DAA0D,CAE9D,CAmBgB,SAAAC,IACd,OAAO,IAAIf,EACTO,EAAiBS,SACjB,oCAAoC,CAExC,CAiCgB,SAAAC,IACd,OAAO,IAAIjB,EACTO,EAAiBW,kBACjB,wDAAwD,CAE5D,CA0BM,SAAUC,EAAgBvD,GAC9B,OAAO,IAAIoC,EAAaO,EAAiBa,iBAAkBxD,CAAO,CACpE,CA+BgB,SAAAyD,KACd,OAAO,IAAIrB,EACTO,EAAiBe,YACjB,+BAA+B,CAEnC,CAOM,SAAUC,GAAqBjI,GACnC,OAAO,IAAI0G,EACTO,EAAiBiB,uBACjB,kBACElI,EAEA,iHAAoD,CAE1D,CAMgB,SAAAmI,EAAcC,EAAgB9D,GAC5C,OAAO,IAAIoC,EACTO,EAAiBoB,eACjB,iCAAmCD,EAAS,MAAQ9D,CAAO,CAE/D,CAYM,SAAUgE,EAAchE,GAC5B,MAAM,IAAIoC,EACRO,EAAiBsB,eACjB,mBAAqBjE,CAAO,CAEhC,EA3QY2C,EAAAA,EAAAA,GA4BX,IA1BC,QAAA,UACAA,EAAA,iBAAA,mBACAA,EAAA,iBAAA,mBACAA,EAAA,kBAAA,oBACAA,EAAA,eAAA,iBACAA,EAAA,gBAAA,kBACAA,EAAA,aAAA,eACAA,EAAA,iBAAA,mBACAA,EAAA,qBAAA,uBACAA,EAAA,iBAAA,mBACAA,EAAA,SAAA,WAEAA,EAAA,mBAAA,qBACAA,EAAA,YAAA,cACAA,EAAA,uBAAA,yBACAA,EAAA,kBAAA,oBACAA,EAAA,kBAAA,oBACAA,EAAA,uBAAA,yBACAA,EAAA,gBAAA,kBACAA,EAAA,iBAAA,mBACAA,EAAA,uBAAA,yBACAA,EAAA,YAAA,cACAA,EAAA,uBAAA,yBACAA,EAAA,eAAA,iBACAA,EAAA,eAAA,iBACAA,EAAA,wBAAA,gCGpFWuB,EAGXzI,YAA4B0I,EAAgBC,GAAhBlL,KAAMiL,OAANA,EAC1BjL,KAAKmL,MAAQD,CACd,CAEDA,WACE,OAAOlL,KAAKmL,KACb,CAEDC,aACE,OAA4B,IAArBpL,KAAKkL,KAAK5L,MAClB,CAED+L,gBACE,IAAMC,EAASC,mBACf,MAAO,MAAQD,EAAOtL,KAAKiL,MAAM,EAAI,MAAQK,EAAOtL,KAAKkL,IAAI,CAC9D,CAEDM,sBAEE,MAAO,MADQD,mBACOvL,KAAKiL,MAAM,EAAI,IACtC,CAEDQ,0BAA0BC,EAAsB5I,GAC9C3D,IAAIwM,EACJ,IACEA,EAAiBX,EAASY,YAAYF,EAAc5I,CAAI,CAKzD,CAJC,MAAO+I,GAGP,OAAO,IAAIb,EAASU,EAAc,EAAE,CACrC,CACD,GAA4B,KAAxBC,EAAeT,KACjB,OAAOS,EAEP,MH8J+BV,EG9JJS,EH+JxB,IAAIxC,EACTO,EAAiBqC,uBACjB,2BAA6Bb,EAAS,IAAI,CG/J3C,CAEDW,mBAAmBG,EAAajJ,GAC9B3D,IAAImE,EAA4B,KAChC,IAAM0I,EAAe,sBAOrB,IACMC,EAAU,IAAIC,OAAO,SAAWF,EADvB,YAC8C,GAAG,EAGhE,SAASG,EAAWC,GAClBA,EAAIjB,MAAQkB,mBAAmBD,EAAIlB,IAAI,CACxC,CACD,IACMoB,EAAsBxJ,EAAKF,QAAQ,OAAQ,KAAK,EAEhD2J,EAAwB,IAAIL,oBACnBI,sBAAoCN,qBACjD,GAAG,EAICQ,EACJ1J,IAASkG,EACL,sDACAlG,EAQA2J,EAAS,CACb,CAAEC,MAAOT,EAASU,QA1BF,CAAE1B,OAAQ,EAAGC,KAAM,CAAC,EA0BE0B,WAjCxC,SAAkBR,GAC6B,MAAzCA,EAAIlB,KAAK9I,OAAOgK,EAAIlB,KAAK5L,OAAS,CAAC,IACrC8M,EAAIjB,MAAQiB,EAAIjB,MAAM0B,MAAM,EAAG,CAAC,CAAC,EAEpC,CA6B6D,EAC5D,CACEH,MAAOH,EACPI,QAjB2B,CAAE1B,OAAQ,EAAGC,KAAM,CAAC,EAkB/C0B,WAAYT,CACb,EACD,CACEO,MAduB,IAAIR,oBAChBM,KAAoBR,aACjC,GAAG,EAaDW,QAXwB,CAAE1B,OAAQ,EAAGC,KAAM,CAAC,EAY5C0B,WAAYT,CACb,GAEH,IAAKhN,IAAIE,EAAI,EAAGA,EAAIoN,EAAOnN,OAAQD,CAAC,GAAI,CACtC,IAAMyN,EAAQL,EAAOpN,GACf0N,EAAWD,EAAMJ,MAAMM,KAAKjB,CAAG,EACrC,GAAIgB,EAAU,CACZ,IAAME,EAAcF,EAASD,EAAMH,QAAQ1B,QAC3C9L,IAAI+N,EAAYH,EAASD,EAAMH,QAAQzB,MAClCgC,EAAAA,GACS,GAEd5J,EAAW,IAAI0H,EAASiC,EAAaC,CAAS,EAC9CJ,EAAMF,WAAWtJ,CAAQ,EACzB,KACD,CACF,CACD,GAAgB,MAAZA,EACF,MHmFqByI,EGnFJA,EHoFd,IAAI7C,EACTO,EAAiB0D,YACjB,gBAAkBpB,EAAM,IAAI,EGpF5B,OAAOzI,CACR,CACF,OCrHY8J,GAGX7K,YAAY8K,GACVrN,KAAKsN,SAAWC,QAAQC,OAAUH,CAAK,CACxC,CAGDI,aACE,OAAOzN,KAAKsN,QACb,CAGDI,OAAOC,EAAAA,IACR,CCJK,SAAUC,EAASxO,GACvB,MAAoB,UAAb,OAAOA,GAAkBA,aAAa6C,MAC/C,CAEM,SAAU4L,GAAazO,GAC3B,OAAO0O,EAAmB,GAAM1O,aAAa2O,IAC/C,CAEgB,SAAAD,IACd,MAAuB,aAAhB,OAAOC,IAChB,CAEM,SAAUC,EACdC,EACAC,EACAC,EACArG,GAEA,GAAIA,EAAQoG,EACV,MAAM7D,wBACkB4D,gBAAuBC,eAAsB,EAGvE,GAAYC,EAARrG,EACF,MAAMuC,wBACkB4D,gBAAuBE,YAAmB,CAGtE,CCtCgB,SAAAC,EACdC,EACAvL,EACAwL,GAEAnP,IAAIoP,EACY,MAAZD,EACO,WAAWxL,EAFTA,EAIb,SAAUwL,OAAcC,OAAYF,CACtC,CAEM,SAAUG,GAAgBC,GAC9B,IAEWhL,EAEDiL,EAJJpD,EAASC,mBACfpM,IAAIwP,EAAY,IAChB,IAAWlL,KAAOgL,EACZA,EAAOG,eAAenL,CAAG,IACrBiL,EAAWpD,EAAO7H,CAAG,EAAI,IAAM6H,EAAOmD,EAAOhL,EAAI,EACvDkL,EAAYA,EAAYD,EAAW,KAMvC,OADAC,EAAYA,EAAU9B,MAAM,EAAG,CAAC,CAAC,CAEnC,CCxBgB,SAAAgC,GACdtF,EACAuF,GAIA,IAAMC,EAA8B,KAAVxF,GAAiBA,EAAS,IAO9CyF,EAAuD,CAAC,IANtC,CAEtB,IAEA,KAEuCC,QAAQ1F,CAAM,EACjD2F,EAAiE,CAAC,IAA1CJ,EAAqBG,QAAQ1F,CAAM,EACjE,OAAOwF,GAAqBC,GAAoBE,CAClD,ENiCYxF,EAAAA,EAAAA,GAIX,IAHCA,EAAA,SAAA,GAAA,WACAA,EAAAA,EAAA,cAAA,GAAA,gBACAA,EAAAA,EAAA,MAAA,GAAA,cOxBIyF,GAUJ5M,YACU6M,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAQ,CAAA,EACRC,EAAkB,CAAA,GAZlBhQ,KAAIoP,KAAJA,EACApP,KAAOqP,QAAPA,EACArP,KAAQsP,SAARA,EACAtP,KAAKuP,MAALA,EACAvP,KAAawP,cAAbA,EACAxP,KAAqByP,sBAArBA,EACAzP,KAAS0P,UAATA,EACA1P,KAAc2P,eAAdA,EACA3P,KAAQ4P,SAARA,EACA5P,KAAiB6P,kBAAjBA,EACA7P,KAAkB8P,mBAAlBA,EACA9P,KAAK+P,MAALA,EACA/P,KAAegQ,gBAAfA,EAtBFhQ,KAAkBiQ,mBAAyB,KAC3CjQ,KAAUkQ,WAAqB,KAI/BlQ,KAASmQ,UAAY,CAAA,EACrBnQ,KAAUoQ,WAAY,CAAA,EAkB5BpQ,KAAKsN,SAAW,IAAIC,QAAQ,CAAC8C,EAAS7C,KACpCxN,KAAKsQ,SAAWD,EAChBrQ,KAAKuQ,QAAU/C,EACfxN,KAAKwQ,OAAM,CACb,CAAC,CACF,CAKOA,SACN,IAAMC,EAGM,CAACC,EAAiBzG,KAC5B,GAAIA,EACFyG,EAAgB,CAAA,EAAO,IAAIC,EAAiB,CAAA,EAAO,KAAM,CAAA,CAAI,CAAC,MADhE,CAIA,IAAMC,EAAa5Q,KAAK8P,qBAGlBe,GAFN7Q,KAAKiQ,mBAAqBW,EAIdE,IACV,IAAMC,EAASD,EAAcC,OACvBC,EAAQF,EAAcG,iBAAmBH,EAAcE,MAAQ,CAAC,EACvC,OAA3BhR,KAAK6P,mBACP7P,KAAK6P,kBAAkBkB,EAAQC,CAAK,CAExC,GAC+B,OAA3BhR,KAAK6P,mBACPe,EAAWM,0BAA0BL,CAAgB,EAKvDD,EACGO,KACCnR,KAAKoP,KACLpP,KAAKqP,QACLrP,KAAKgQ,gBACLhQ,KAAKuP,MACLvP,KAAKsP,QAAQ,EAEd8B,KAAK,KAC2B,OAA3BpR,KAAK6P,mBACPe,EAAWS,6BAA6BR,CAAgB,EAE1D7Q,KAAKiQ,mBAAqB,KAC1B,IAAMqB,EAAYV,EAAWW,aAAY,IAAO7H,EAAU8H,SACpDjI,EAASqH,EAAWa,YAExB,CAACH,GACAzC,GAAkBtF,EAAQvJ,KAAKyP,qBAAqB,GACnDzP,KAAK+P,OAED2B,EAAcd,EAAWW,aAAY,IAAO7H,EAAUiI,MAC5DjB,EACE,CAAA,EACA,IAAIC,EAAiB,CAAA,EAAO,KAAMe,CAAW,CAAC,IAI5CE,EAAqD,CAAC,IAAxC5R,KAAKwP,cAAcP,QAAQ1F,CAAM,EACrDmH,EAAgB,CAAA,EAAM,IAAIC,EAAiBiB,EAAahB,CAAU,CAAC,EACrE,CAAC,CAhDF,CAiDH,EAMMiB,EAGM,CAACC,EAAoBvI,KAC/B,IAAM8G,EAAUrQ,KAAKsQ,SACf9C,EAASxN,KAAKuQ,QACdK,EAAarH,EAAOqH,WAC1B,GAAIrH,EAAOwI,eACT,IACE,IAAMC,EAAShS,KAAK0P,UAAUkB,EAAYA,EAAWqB,YAAW,CAAE,EH1I7D,KAAA,IG2ISD,EACZ3B,EAAQ2B,CAAM,EAEd3B,GAIH,CAFC,MAAOxE,GACP2B,EAAO3B,CAAC,CACT,MAEkB,OAAf+E,IACIsB,EAAMrI,KACRR,eAAiBuH,EAAWuB,eAC5BnS,KAAK2P,eACPnC,EAAOxN,KAAK2P,eAAeiB,EAAYsB,CAAG,CAAC,EAE3C1E,EAAO0E,CAAG,GAGR3I,EAAOU,SAETuD,GADYxN,KAAKoQ,WAAa7F,GAAeN,GAAH,CAChC,EAGVuD,EADYzD,GACF,CAIlB,EACI/J,KAAKmQ,UACP0B,EAAY,EAAO,IAAIlB,EAAiB,CAAA,EAAO,KAAM,CAAA,CAAI,CAAC,EAE1D3Q,KAAKkQ,YCzJL,CACJkC,EAKAC,EACAC,KAIAnT,IAAIoT,EAAc,EAIdC,EAAsB,KAEtBC,EAAuB,KACvBC,EAAa,CAAA,EACbC,EAAc,EAElB,SAAS1I,IACP,OAAuB,IAAhB0I,CACR,CACDxT,IAAIyT,EAAoB,CAAA,EAExB,SAASC,KAAmBC,GACrBF,IACHA,EAAoB,CAAA,EACpBP,EAAkBU,MAAM,KAAMD,CAAI,EAErC,CAED,SAASE,EAAcC,GACrBT,EAAiBU,WAAW,KAC1BV,EAAiB,KACjBJ,EAAUe,EAAiBlJ,EAAQ,CAAE,CACtC,EAAEgJ,CAAM,CACV,CAED,SAASG,IACHX,GACFY,aAAaZ,CAAe,CAE/B,CAED,SAASU,EAAgBG,KAAqBR,GAC5C,GAAIF,EACFQ,SAGF,GAAIE,EACFF,IACAP,EAAgBU,KAAK,KAAMD,EAAS,GAAGR,CAAI,OAI7C,GADiB7I,EAAU,GAAIyI,EAE7BU,IACAP,EAAgBU,KAAK,KAAMD,EAAS,GAAGR,CAAI,MAF7C,CAKIP,EAAc,KAEhBA,GAAe,GAEjBpT,IAAIqU,EAOJR,EAJEQ,EAFkB,IAAhBb,GACFA,EAAc,EACD,GAEgC,KAA/BJ,EAAckB,KAAKC,OAAM,EAEjB,CAZvB,CAaF,CACDvU,IAAIwU,EAAU,CAAA,EAEd,SAASC,EAAKC,GACRF,IAGJA,EAAU,CAAA,EACVP,IACIR,KAGmB,OAAnBJ,GACGqB,IACHlB,EAAc,GAEhBU,aAAab,CAAc,EAC3BQ,EAAc,CAAC,GAEVa,IACHlB,EAAc,GAGnB,CAMD,OALAK,EAAc,CAAC,EACfP,EAAkBS,WAAW,KAE3BU,EADAlB,EAAa,CAAA,CACJ,CACV,EAAEJ,CAAO,EACHsB,CACT,GDiD8BnD,EAAcoB,EAAa7R,KAAK4P,QAAQ,CAEnE,CAGDnC,aACE,OAAOzN,KAAKsN,QACb,CAGDI,OAAOoG,GACL9T,KAAKmQ,UAAY,CAAA,EACjBnQ,KAAKoQ,WAAa0D,GAAa,CAAA,EACP,OAApB9T,KAAKkQ,aCpDXlM,EDqDShE,KAAKkQ,YCrDX,CAAA,CAAK,EDuD0B,OAA5BlQ,KAAKiQ,oBACPjQ,KAAKiQ,mBAAmB8D,OAE3B,CACF,OAMYpD,EAMXpO,YACSwP,EACAnB,EACP3G,GAFOjK,KAAc+R,eAAdA,EACA/R,KAAU4Q,WAAVA,EAGP5Q,KAAKiK,SAAW,CAAC,CAACA,CACnB,CACF,CE7Le,SAAA+J,MAAWlB,GACzB,IAAMmB,EAhBqB,aAAvB,OAAOA,YACFA,YAC+B,aAA7B,OAAOC,kBACTA,kBADF,KAAA,EAeP,GAAoBC,KAAAA,IAAhBF,EAA2B,CAC7B,IAAMG,EAAK,IAAIH,EACf,IAAK9U,IAAIE,EAAI,EAAGA,EAAIyT,EAAKxT,OAAQD,CAAC,GAChC+U,EAAG7N,OAAOuM,EAAKzT,EAAE,EAEnB,OAAO+U,EAAGJ,SACX,CACC,GAAIlG,EAAmB,EACrB,OAAO,IAAIC,KAAK+E,CAAI,EAEpB,MAAM,IAAI5J,EACRO,EAAiB4K,wBACjB,qDAAqD,CAI7D,CCtCM,SAAUC,GAAaC,GAC3B,GAAoB,aAAhB,OAAOpU,KACT,MXkPK,IAAI+I,EACTO,EAAiB4K,wBACjB,+JAAmK,EWlPrK,OAAOlU,KAAKoU,CAAO,CACrB,CCIa,IAAAC,EAAe,CAQ1BC,IAAK,MAOLC,OAAQ,SAORC,UAAW,YAUXC,SAAU,UACD,QAEEC,EAGXtS,YAAmBkF,EAAkBqN,GAAlB9U,KAAIyH,KAAJA,EACjBzH,KAAK8U,YAAcA,GAAe,IACnC,CACF,CAKe,SAAAC,GACdnK,EACAoK,GAEA,OAAQpK,GACN,KAAK4J,EAAaC,IAChB,OAAO,IAAII,EAAWI,GAAWD,CAAU,CAAC,EAC9C,KAAKR,EAAaE,OAClB,KAAKF,EAAaG,UAChB,OAAO,IAAIE,EAAWK,GAAatK,EAAQoK,CAAU,CAAC,EACxD,KAAKR,EAAaI,SAChB,OAAO,IAAIC,GAwIaM,EAvIRH,GAwIdI,EAAQ,IAAIC,GAAaF,CAAO,GAC5B1V,OACDyV,GAAaV,EAAaE,OAAQU,EAAME,IAAI,GArFlBxN,IACnC3I,IAAIoW,EACJ,IACEA,EAAUlJ,mBAAmBvE,CAAK,CAGnC,CAFC,MAAO+D,GACP,MAAMlB,EAAc6J,EAAaI,SAAU,qBAAqB,CACjE,CACD,OAAOK,GAAWM,CAAO,CAC3B,GA+EgCH,EAAME,IAAI,IAINH,EA/IRH,EAgJZ,IAAIK,GAAaF,CAAO,EACzBL,YAjJwB,CAIpC,CA2IG,IATwBK,EACtBC,EAhIN,MAAMvL,EAAO,CACf,CAEM,SAAUoL,GAAWnN,GACzB,IAiBgB0N,EACAC,EAlBVC,EAAc,GACpB,IAAKvW,IAAIE,EAAI,EAAGA,EAAIyI,EAAMxI,OAAQD,CAAC,GAAI,CACrCF,IAAII,EAAIuI,EAAMtI,WAAWH,CAAC,EACtBE,GAAK,IACPmW,EAAEtU,KAAK7B,CAAC,EAEJA,GAAK,KACPmW,EAAEtU,KAAK,IAAO7B,GAAK,EAAI,IAAW,GAAJA,CAAO,EAEjB,QAAX,MAAJA,GAGDF,EAAIyI,EAAMxI,OAAS,GAA2C,QAAX,MAA1BwI,EAAMtI,WAAWH,EAAI,CAAC,IAKzCmW,EAAKjW,EACLkW,EAAK3N,EAAMtI,WAAW,EAAEH,CAAC,EAC/BE,EAAI,OAAe,KAALiW,IAAc,GAAY,KAALC,EACnCC,EAAEtU,KACA,IAAO7B,GAAK,GACZ,IAAQA,GAAK,GAAM,GACnB,IAAQA,GAAK,EAAK,GAClB,IAAW,GAAJA,CAAO,GAThBmW,EAAEtU,KAAK,IAAK,IAAK,GAAG,EAaF,QAAX,MAAJ7B,GAEHmW,EAAEtU,KAAK,IAAK,IAAK,GAAG,EAEpBsU,EAAEtU,KAAK,IAAO7B,GAAK,GAAK,IAAQA,GAAK,EAAK,GAAK,IAAW,GAAJA,CAAO,CAKtE,CACD,OAAO,IAAIoW,WAAWD,CAAC,CACzB,CAYgB,SAAAR,GAAatK,EAAsB9C,GACjD,OAAQ8C,GACN,KAAK4J,EAAaE,OAChB,IAAMkB,EAAkC,CAAC,IAAxB9N,EAAMmH,QAAQ,GAAG,EAC5B4G,EAAkC,CAAC,IAAxB/N,EAAMmH,QAAQ,GAAG,EAClC,GAAI2G,GAAYC,EAEd,MAAMlL,EACJC,EACA,uBAHkBgL,EAAW,IAAM,KAKjC,mCAAmC,EAGzC,MAEF,KAAKpB,EAAaG,UACVmB,EAAiC,CAAC,IAAxBhO,EAAMmH,QAAQ,GAAG,EAC3B8G,EAAkC,CAAC,IAAxBjO,EAAMmH,QAAQ,GAAG,EAClC,GAAI6G,GAAWC,EAEb,MAAMpL,EACJC,EACA,uBAHkBkL,EAAU,IAAM,KAGI,gCAAgC,EAG1EhO,EAAQA,EAAMlF,QAAQ,KAAM,GAAG,EAAEA,QAAQ,KAAM,GAAG,CAKrD,CACDzD,IAAIuC,EACJ,IACEA,EAAQ4S,GAAaxM,CAAK,CAM3B,CALC,MAAO+D,GACP,GAAKA,EAAY/E,QAAQkP,SAAS,UAAU,EAC1C,MAAMnK,EAER,MAAMlB,EAAcC,EAAQ,yBAAyB,CACtD,CACD,IAAMqL,EAAQ,IAAIN,WAAWjU,EAAMpC,MAAM,EACzC,IAAKH,IAAIE,EAAI,EAAGA,EAAIqC,EAAMpC,OAAQD,CAAC,GACjC4W,EAAM5W,GAAKqC,EAAMlC,WAAWH,CAAC,EAE/B,OAAO4W,CACT,OAEMZ,GAKJ9S,YAAY2T,GAJZlW,KAAMP,OAAY,CAAA,EAClBO,KAAW8U,YAAkB,KAI3B,IAAMqB,EAAUD,EAAQE,MAAM,iBAAiB,EAC/C,GAAgB,OAAZD,EACF,MAAMxL,EACJ6J,EAAaI,SACb,uDAAuD,EAG3D,IAyBcyB,EAAWC,EAzBnBC,EAASJ,EAAQ,IAAM,KACf,MAAVI,IACFvW,KAAKP,QAuBkB6W,EAvBQ,WAuBnBD,EAvBWE,GAwBNjX,QAAUgX,EAAIhX,QAK5B+W,EAAEG,UAAUH,EAAE/W,OAASgX,EAAIhX,MAAM,IAAMgX,GA5B1CtW,KAAK8U,YAAc9U,KAAKP,OACpB8W,EAAOC,UAAU,EAAGD,EAAOjX,OAAS,UAAUA,MAAM,EACpDiX,GAENvW,KAAKsV,KAAOY,EAAQM,UAAUN,EAAQjH,QAAQ,GAAG,EAAI,CAAC,CACvD,CACF,OC3LYwH,EAKXlU,YAAYkF,EAAuCiP,GACjDvX,IAAIwX,EAAe,EACfC,EAAmB,GACnB/I,GAAapG,CAAI,GACnBzH,KAAK6W,MAAQpP,EACbkP,EAAQlP,EAAckP,KACtBC,EAAYnP,EAAcW,MACjBX,aAAgBqP,aACrBJ,EACF1W,KAAK6W,MAAQ,IAAIlB,WAAWlO,CAAI,GAEhCzH,KAAK6W,MAAQ,IAAIlB,WAAWlO,EAAKsP,UAAU,EAC3C/W,KAAK6W,MAAMG,IAAI,IAAIrB,WAAWlO,CAAI,CAAC,GAErCkP,EAAO3W,KAAK6W,MAAMvX,QACTmI,aAAgBkO,aACrBe,EACF1W,KAAK6W,MAAQpP,GAEbzH,KAAK6W,MAAQ,IAAIlB,WAAWlO,EAAKnI,MAAM,EACvCU,KAAK6W,MAAMG,IAAIvP,CAAkB,GAEnCkP,EAAOlP,EAAKnI,QAEdU,KAAKiX,MAAQN,EACb3W,KAAKkX,MAAQN,CACd,CAEDD,OACE,OAAO3W,KAAKiX,KACb,CAED7O,OACE,OAAOpI,KAAKkX,KACb,CAEDrK,MAAMsK,EAAmBC,GACvB,IAQQvK,EHdcwK,EAAYC,EAAehB,EGMjD,OAAIzI,GAAa7N,KAAK6W,KAAK,GACnBU,EAAWvX,KAAK6W,MHPUS,EGQGH,EHRYb,EGQDc,EAC/B,QADTI,GHRcH,EGQKE,GHPpBE,YACAJ,EAAKI,YAAYH,EAAOhB,CAAG,EACzBe,EAAKK,SACPL,EAAKK,SAASJ,EAAOhB,CAAG,EACtBe,EAAKxK,MACPwK,EAAKxK,MAAMyK,EAAOhB,CAAG,EAEvB,MGEM,KAEF,IAAIG,EAAQe,CAAM,IAEnB3K,EAAQ,IAAI8I,WACf3V,KAAK6W,MAAqBc,OAC3BR,EACAC,EAAUD,CAAS,EAEd,IAAIV,EAAQ5J,EAAO,CAAA,CAAI,EAEjC,CAEDmH,kBAAkBlB,GAChB,GAAIhF,EAAmB,EAUrB,OATM8J,EAA4C9E,EAAK+E,IACrD,GACMC,aAAerB,EACVqB,EAAIjB,MAEJiB,CAEV,EAEI,IAAIrB,EAAQzC,GAAQjB,MAAM,KAAM6E,CAAM,CAAC,EACzC,CACL,IAAMG,EAA4BjF,EAAK+E,IACrC,GACMjK,EAASkK,CAAG,EACP/C,GAAeP,EAAaC,IAAKqD,CAAa,EAAErQ,KAG/CqQ,EAAgBjB,KAE3B,EAEH1X,IAAI6Y,EAAc,EAIZC,GAHNF,EAAYG,QAAQ,IAClBF,GAAe/B,EAAMc,UACvB,CAAC,EACc,IAAIpB,WAAWqC,CAAW,GACrCG,EAAQ,EAMZ,OALAJ,EAAYG,QAAQ,IAClB,IAAK/Y,IAAIE,EAAI,EAAGA,EAAI4W,EAAM3W,OAAQD,CAAC,GACjC4Y,EAAOE,CAAK,IAAMlC,EAAM5W,EAE5B,CAAC,EACM,IAAIoX,EAAQwB,EAAQ,CAAA,CAAI,CAChC,CACF,CAEDG,aACE,OAAOpY,KAAK6W,KACb,CACF,CC/GK,SAAUwB,GACdhC,GAEAlX,IAAImZ,EACJ,IACEA,EAAMC,KAAKC,MAAMnC,CAAC,CAGnB,CAFC,MAAOxK,GACP,OAAO,IACR,CACD,MTFoB,UAAb,OADwBzM,ESGVkZ,ITFY/X,MAAMC,QAAQpB,CAAC,ESKvC,KAFAkZ,CAIX,CCkBM,SAAUG,GAAcvN,GAC5B,IAAMiN,EAAQjN,EAAKwN,YAAY,IAAKxN,EAAK5L,OAAS,CAAC,EACnD,MAAc,CAAC,IAAX6Y,EACKjN,EAEAA,EAAK2B,MAAMsL,EAAQ,CAAC,CAE/B,CC/BgB,SAAAQ,GAAYC,EAAoB9Q,GAC9C,OAAOA,CACT,OAEM+Q,EAKJtW,YACSuW,EACPC,EACAC,EACAC,GAHOjZ,KAAM8Y,OAANA,EAKP9Y,KAAK+Y,MAAQA,GAASD,EACtB9Y,KAAKgZ,SAAW,CAAC,CAACA,EAClBhZ,KAAKiZ,MAAQA,GAASN,EACvB,CACF,CAKDxZ,IAAI+Z,GAA6B,KAUjB,SAAAC,IACd,IAGMC,EA6BAC,EAaN,OA7CIH,MAGEE,EAAqB,IAClBhY,KAAK,IAAIyX,EAAgB,QAAQ,CAAC,EAC3CO,EAAShY,KAAK,IAAIyX,EAAgB,YAAY,CAAC,EAC/CO,EAAShY,KAAK,IAAIyX,EAAgB,gBAAgB,CAAC,EACnDO,EAAShY,KAAK,IAAIyX,EAAgB,OAAQ,WAAY,CAAA,CAAI,CAAC,GAQrDS,EAAc,IAAIT,EAAgB,MAAM,GAClCI,MAPZ,SACEM,EACAC,GAEA,MArBE,CAAC5L,EADmB4L,EAsBLA,CArBG,GAAKA,EAASla,OAAS,EACpCka,EAEAf,GAAce,CAAQ,CAmB9B,EAGDJ,EAAShY,KAAKkY,CAAW,GAenBD,EAAc,IAAIR,EAAgB,MAAM,GAClCI,MAXZ,SACEM,EACA5C,GAEA,OAAaxC,KAAAA,IAATwC,EACK8C,OAAO9C,CAAI,EAEXA,CAEV,EAGDyC,EAAShY,KAAKiY,CAAW,EACzBD,EAAShY,KAAK,IAAIyX,EAAgB,aAAa,CAAC,EAChDO,EAAShY,KAAK,IAAIyX,EAAgB,SAAS,CAAC,EAC5CO,EAAShY,KAAK,IAAIyX,EAAgB,UAAW,KAAM,CAAA,CAAI,CAAC,EACxDO,EAAShY,KAAK,IAAIyX,EAAgB,eAAgB,KAAM,CAAA,CAAI,CAAC,EAC7DO,EAAShY,KAAK,IAAIyX,EAAgB,qBAAsB,KAAM,CAAA,CAAI,CAAC,EACnEO,EAAShY,KAAK,IAAIyX,EAAgB,kBAAmB,KAAM,CAAA,CAAI,CAAC,EAChEO,EAAShY,KAAK,IAAIyX,EAAgB,kBAAmB,KAAM,CAAA,CAAI,CAAC,EAChEO,EAAShY,KAAK,IAAIyX,EAAgB,cAAe,KAAM,CAAA,CAAI,CAAC,EAC5DO,EAAShY,KAAK,IAAIyX,EAAgB,WAAY,iBAAkB,CAAA,CAAI,CAAC,EACrEK,GAAYE,GACLF,EACT,CAEgB,SAAAQ,GAAOd,EAAoBtR,GAOzCzD,OAAO8V,eAAef,EAAU,MAAO,CAAEgB,IANzC,WACE,IAAM3O,EAAiB2N,EAAiB,OAClC1N,EAAe0N,EAAmB,SAClCxM,EAAM,IAAIpB,EAASC,EAAQC,CAAI,EACrC,OAAO5D,EAAQuS,sBAAsBzN,CAAG,CACzC,CACwD,CAAE,CAC7D,CAqBgB,SAAA0N,GACdxS,EACAyS,EACAX,GAEA,IAAMd,EAAMD,GAAiB0B,CAAc,EAC3C,GAAY,OAARzB,EACF,OAAO,KAET,IA3BAhR,EA4BoBA,EA3BpB0S,EA0BiB1B,EAzBjBc,EA0BuCA,EAxBjCR,EAAqB,CAC3BxQ,KAAmB,MADQ,EAErB6R,EAAMb,EAAS9Z,OACrB,IAAKH,IAAIE,EAAI,EAAGA,EAAI4a,EAAK5a,CAAC,GAAI,CAC5B,IAAM6a,EAAUd,EAAS/Z,GACzBuZ,EAASsB,EAAQnB,OAAUmB,EAA6BjB,MACtDL,EACAoB,EAASE,EAAQpB,OAAO,CAE3B,CAED,OADAY,GAAOd,EAAUtR,CAAO,EACjBsR,CAcT,CAqCgB,SAAAuB,GACdvB,EACAQ,GAEA,IAAMY,EAEF,GACEC,EAAMb,EAAS9Z,OACrB,IAAKH,IAAIE,EAAI,EAAGA,EAAI4a,EAAK5a,CAAC,GAAI,CAC5B,IAAM6a,EAAUd,EAAS/Z,GACrB6a,EAAQlB,WACVgB,EAASE,EAAQpB,QAAUF,EAASsB,EAAQnB,OAE/C,CACD,OAAOR,KAAK6B,UAAUJ,CAAQ,CAChC,CCjKA,IAAMK,GAAe,WACfC,GAAY,QAiCF,SAAAC,GACdjT,EACA2D,EACA8O,GAEA,IAAMzB,EAAMD,GAAiB0B,CAAc,EAC3C,GAAY,OAARzB,EACF,OAAO,KAET,IAvCAhR,EAwC2BA,EAvC3B2D,EAuCoCA,EAtCpC+O,EAqCiB1B,EAnCXkC,EAAyB,CAC7BC,SAAU,GACVC,MAAO,GACPC,cAAeX,EAAwB,eAEzC,GAAIA,EAASK,IACX,IAAK,IAAMnP,KAAQ8O,EAASK,IAAe,CACnCO,EAA2B1P,EAAKtI,QAAQ,MAAO,EAAE,EACjDiY,EAAYvT,EAAQuS,sBACxB,IAAI7O,EAASC,EAAQ2P,CAAwB,CAAC,EAEhDJ,EAAWC,SAASrZ,KAAKyZ,CAAS,CACnC,CAGH,GAAIb,EAASM,IACX,IAAK,IAAMQ,KAAQd,EAASM,IAAY,CAChCO,EAAYvT,EAAQuS,sBACxB,IAAI7O,EAASC,EAAQ6P,EAAW,IAAC,CAAC,EAEpCN,EAAWE,MAAMtZ,KAAKyZ,CAAS,CAChC,CAEH,OAAOL,CAcT,OCvCaO,EAcXxY,YACSwJ,EACAiP,EAQAC,EACA3I,GAVAtS,KAAG+L,IAAHA,EACA/L,KAAMgb,OAANA,EAQAhb,KAAOib,QAAPA,EACAjb,KAAOsS,QAAPA,EAxBTtS,KAASkb,UAAc,GACvBlb,KAAOmb,QAAY,GACnBnb,KAAIwG,KAAsC,KAC1CxG,KAAYob,aAAwB,KAMpCpb,KAAgBqb,iBAA8C,KAC9Drb,KAAAsb,aAAyB,CAAC,KAC1Btb,KAAoB8O,qBAAa,EAc7B,CACL,CCzBK,SAAUyM,EAAaC,GAC3B,GAAI,CAACA,EACH,MAAM3R,EAAO,CAEjB,CAEgB,SAAA4R,EACdnU,EACA8R,GAOA,OALA,SAAiBsC,EAAyBC,GACxC,IAAM/C,EAAWkB,GAAmBxS,EAASqU,EAAMvC,CAAQ,EAE3D,OADAmC,EAA0B,OAAb3C,CAAiB,EACvBA,CACR,CAEH,CAEgB,SAAAgD,GACdtU,EACA2D,GAOA,OALA,SAAiByQ,EAAyBC,GACxC,IAAMnB,EAAaD,GAAmBjT,EAAS2D,EAAQ0Q,CAAI,EAE3D,OADAJ,EAA4B,OAAff,CAAmB,EACzBA,CACR,CAEH,CAEgB,SAAAqB,GACdvU,EACA8R,GAYA,OAVA,SAAiBsC,EAAyBC,GACxC,IAAM/C,EAAWkB,GAAmBxS,EAASqU,EAAMvC,CAAQ,EAC3DmC,EAA0B,OAAb3C,CAAiB,EACvBkD,CAAAA,IHmETlD,EGlEIA,EHoEJ9V,EGlEIwE,EAAQxE,KHmEZwL,EGlEIhH,EAAQyU,UHqEZ,GAAY,QADNzD,EAAMD,GAAiB0B,CAAc,GAEzC,OAAO,KAET,GAAI,CAACnM,EAAS0K,EAAoB,cAAC,EAGjC,OAAO,KAGT,GAAsB,KADhB0D,EAAiB1D,EAAoB,gBAChChZ,OACT,OAAO,KAET,IAAMgM,EAASC,mBAaf,OAZmByQ,EAAOC,MAAM,GAAG,EACXpE,IAAI,IAC1B,IAAM5M,EAAiB2N,EAAiB,OAClC1N,EAAe0N,EAAmB,SAOxC,OALaxK,EADG,MAAQ9C,EAAOL,CAAM,EAAI,MAAQK,EAAOJ,CAAI,EAC9BpI,EAAMwL,CAAQ,EACxBE,GAAgB,CAClC0N,IAAK,QACLC,MAAAA,CACD,CAAA,CAEH,CAAC,EACW,EG9FS,CAEpB,CAEH,CAEM,SAAUC,EACd9Y,GAgCA,OA9BA,SACEoY,EACAxJ,GAEA/S,IAAIkd,EnBmEF,IAxBwBpR,EmBnB1B,OAjBIoR,EANoB,MAApBX,EAAIjK,UAAW,EAIfiK,EAAIvJ,aAAY,EAAG6D,SAAS,qCAAqC,EnBwDhE,IAAI9M,EACTO,EAAiB6S,iBACjB,+EAA+E,EAN1E,IAAIpT,EAAaO,EAAiB8S,gBAFvC,6FAE+D,EmB7CrC,MAApBb,EAAIjK,UAAW,GnB+BKxG,EmB9BC3H,EAAS2H,OnB+B/B,IAAI/B,EACTO,EAAiB+S,eACjB,qBACEvR,EAEA,wEAAuC,GmBlCb,MAApByQ,EAAIjK,UAAW,GnBoDEvG,EmBnDG5H,EAAS4H,KnBoDhC,IAAIhC,EACTO,EAAiBgT,aACjB,4CAA8CvR,EAAO,IAAI,GmBpD1CgH,GAIR3I,OAASmS,EAAIjK,YACpB4K,EAAOhT,eAAiB6I,EAAI7I,eACrBgT,CACR,CAEH,CAEM,SAAUK,EACdpZ,GAEA,IAAMqZ,EAASP,EAAmB9Y,CAAQ,EAa1C,OAXA,SACEoY,EACAxJ,GAEA/S,IAAIkd,EAASM,EAAOjB,EAAKxJ,CAAG,EAK5B,OAHEmK,EADsB,MAApBX,EAAIjK,UAAW,GnBjBQvG,EmBkBD5H,EAAS4H,KnBjB9B,IAAIhC,EACTO,EAAiBmT,iBACjB,WAAa1R,EAAO,mBAAmB,GmBiBvCmR,GAAOhT,eAAiB6I,EAAI7I,eACrBgT,CACR,CAEH,CAEgBQ,SAAAA,GACdvV,EACAhE,EACA8V,GAEA,IACMrN,EAAMqC,EADI9K,EAAS+H,gBACI/D,EAAQxE,KAAMwE,EAAQyU,SAAS,EAEtDzJ,EAAUhL,EAAQwV,sBAClBC,EAAc,IAAIhC,EACtBhP,EAHa,MAKb0P,EAAgBnU,EAAS8R,CAAQ,EACjC9G,CAAO,EAGT,OADAyK,EAAY3B,aAAesB,EAAmBpZ,CAAQ,EAC/CyZ,CACT,CAoIgB,SAAAC,GACd1Z,EACA+T,EACAuB,GAEA,IAAMqE,EAAgBpZ,OAAOqZ,OAAO,GAAItE,CAAQ,EAMhD,OALAqE,EAAwB,SAAI3Z,EAAS4H,KACrC+R,EAAoB,KAAI5F,EAAKV,KAAI,EAC5BsG,EAA2B,cAC9BA,EAA2B,aAlB7B5F,EAkB6DA,GAnB7DuB,EAmBuD,OAfxCA,EAAsB,aAClCvB,GAAQA,EAAKjP,QACd,6BAeK6U,CACT,CAKM,SAAUE,GACd7V,EACAhE,EACA8V,EACA/B,EACAuB,GAEA,IAAMvK,EAAU/K,EAASkI,sBACnB2P,EAAsC,CAC1CiC,yBAA0B,aAU5B,IAAMC,GAPN,KACEle,IAAIF,EAAM,GACV,IAAKE,IAAIE,EAAI,EAAGA,EAAI,EAAGA,CAAC,GACtBJ,GAAYwU,KAAKC,OAAM,EAAG4J,WAAWzQ,MAAM,CAAC,EAE9C,OAAO5N,CACR,KAGKse,GADNpC,EAAQ,gBAAkB,+BAAiCkC,EACzCL,GAAmB1Z,EAAU+T,EAAMuB,CAAQ,GAEvD4E,EACJ,KACAH,EAEA,4DALqBlD,GAAiBoD,EAAWnE,CAAQ,EAOzD,SACAiE,EAEA,qBACAE,EAAuB,YACvB,WACIE,EAAe,SAAWJ,EAAW,KACrC7W,EAAOiQ,EAAQzC,QAAQwJ,EAAanG,EAAMoG,CAAY,EAC5D,GAAa,OAATjX,EACF,MAAM2D,EAAe,EAEjB+Q,EAAuB,CAAE1Y,KAAM+a,EAAoB,QAAE,EACrDxR,EAAMqC,EAAQC,EAAS/G,EAAQxE,KAAMwE,EAAQyU,SAAS,EAEtDzJ,EAAUhL,EAAQoW,mBAClBX,EAAc,IAAIhC,EACtBhP,EAHa,OAKb0P,EAAgBnU,EAAS8R,CAAQ,EACjC9G,CAAO,EAMT,OAJAyK,EAAY7B,UAAYA,EACxB6B,EAAY5B,QAAUA,EACtB4B,EAAYvW,KAAOA,EAAK4R,aACxB2E,EAAY3B,aAAegB,EAAmB9Y,CAAQ,EAC/CyZ,CACT,OASaY,EAIXpb,YACSqb,EACA5M,EACP6M,EACAjF,GAHO5Y,KAAO4d,QAAPA,EACA5d,KAAKgR,MAALA,EAIPhR,KAAK6d,UAAY,CAAC,CAACA,EACnB7d,KAAK4Y,SAAWA,GAAY,IAC7B,CACF,CAEe,SAAAkF,GACdpC,EACAqC,GAEA5e,IAAIoK,EAAwB,KAC5B,IACEA,EAASmS,EAAIsC,kBAAkB,sBAAsB,CAGtD,CAFC,MAAOnS,GACP0P,EAAa,CAAA,CAAK,CACnB,CAGD,OADAA,EAAa,CAAC,CAAChS,GAA4C,CAAC,KADtCwU,GAAW,CAAC,WACK9O,QAAQ1F,CAAM,CAAQ,EACtDA,CACT,CAEM,SAAU0U,GACd3W,EACAhE,EACA8V,EACA/B,EACAuB,GAEA,IAAMvK,EAAU/K,EAASkI,sBACnB0S,EAAoBlB,GAAmB1Z,EAAU+T,EAAMuB,CAAQ,EAC/DsC,EAAuB,CAAE1Y,KAAM0b,EAA4B,QAAE,EAC7DnS,EAAMqC,EAAQC,EAAS/G,EAAQxE,KAAMwE,EAAQyU,SAAS,EAEtDZ,EAAU,CACdiC,yBAA0B,YAC1Be,wBAAyB,QACzBC,sCAAuC,GAAG/G,EAAKV,KAAM,EACrD0H,oCAAqCH,EAA+B,YACpEI,eAAgB,mCAEZ9X,EAAO2T,GAAiB+D,EAAmB9E,CAAQ,EACnD9G,EAAUhL,EAAQoW,mBAalBX,EAAc,IAAIhC,EAAYhP,EAtBrB,OAWf,SAAiB2P,GACfoC,GAAmBpC,CAAG,EACtBvc,IAAI4M,EACJ,IACEA,EAAM2P,EAAIsC,kBAAkB,mBAAmB,CAGhD,CAFC,MAAOnS,GACP0P,EAAa,CAAA,CAAK,CACnB,CAED,OADAA,EAAa3N,EAAS7B,CAAG,CAAC,EACnBA,CACR,EACyDuG,CAAO,EAKjE,OAJAyK,EAAY7B,UAAYA,EACxB6B,EAAY5B,QAAUA,EACtB4B,EAAYvW,KAAOA,EACnBuW,EAAY3B,aAAegB,EAAmB9Y,CAAQ,EAC/CyZ,CACT,CAKM,SAAUwB,GACdjX,EACAhE,EACAyI,EACAsL,GAsBA,IACM/E,EAAUhL,EAAQoW,mBAClBX,EAAc,IAAIhC,EAAYhP,EAFrB,OAlBf,SAAiB2P,GACf,IAAMnS,EAASuU,GAAmBpC,EAAK,CAAC,SAAU,QAAQ,EAC1Dvc,IAAIqf,EAA4B,KAChC,IACEA,EAAa9C,EAAIsC,kBAAkB,6BAA6B,CAGjE,CAFC,MAAOnS,GACP0P,EAAa,CAAA,CAAK,CACnB,CAEIiD,GAEHjD,EAAa,CAAA,CAAK,EAGpB,IAAM5E,EAAO8C,OAAO+E,CAAU,EAE9B,OADAjD,EAAa,CAACkD,MAAM9H,CAAI,CAAC,EAClB,IAAIgH,EAAsBhH,EAAMU,EAAKV,OAAmB,UAAXpN,CAAkB,CACvE,EAGyD+I,CAAO,EAGjE,OAFAyK,EAAY5B,QAvBI,CAAEgD,wBAAyB,SAwB3CpB,EAAY3B,aAAegB,EAAmB9Y,CAAQ,EAC/CyZ,CACT,CAiBgB,SAAA2B,GACdpb,EACAgE,EACAyE,EACAsL,EACAsH,EACAvF,EACA7P,EACA8R,GAIA,IAAMlS,EAAU,IAAIwU,EAAsB,EAAG,CAAC,EAQ9C,GAPIpU,GACFJ,EAAQyU,QAAUrU,EAAOqU,QACzBzU,EAAQ6H,MAAQzH,EAAOyH,QAEvB7H,EAAQyU,QAAU,EAClBzU,EAAQ6H,MAAQqG,EAAKV,QAEnBU,EAAKV,SAAWxN,EAAQ6H,MAC1B,MnBtRK,IAAI9H,EACTO,EAAiBmV,uBACjB,sEAAsE,EmBsRxE,IAAMC,EAAY1V,EAAQ6H,MAAQ7H,EAAQyU,QAC1Cze,IAAI2f,EAAgBD,EACJ,EAAZF,IACFG,EAAgBrL,KAAKsL,IAAID,EAAeH,CAAS,GAEnD,IAAMxH,EAAYhO,EAAQyU,QACpBxG,EAAUD,EAAY2H,EAC5B3f,IAAI6f,EAAgB,GAQd7D,EAAU,CACdgD,wBAPAa,EADoB,IAAlBF,EACc,WACPD,IAAcC,EACP,mBAEA,SAIhBG,uBAAwB,GAAG9V,EAAQyU,SAE/BpX,EAAO6Q,EAAKxK,MAAMsK,EAAWC,CAAO,EAC1C,GAAa,OAAT5Q,EACF,MAAM2D,EAAe,EA4BjBmI,EAAUhL,EAAQoW,mBAClBX,EAAc,IAAIhC,EAAYhP,EAFrB,OAxBf,SACE2P,EACAC,GAMA,IAAMuD,EAAepB,GAAmBpC,EAAK,CAAC,SAAU,QAAQ,EAC1DyD,EAAahW,EAAQyU,QAAUkB,EAC/BnI,EAAOU,EAAKV,OAClBxX,IAAIyZ,EAMJ,OAJEA,EADmB,UAAjBsG,EACSzD,EAAgBnU,EAAS8R,CAAQ,EAAEsC,EAAKC,CAAI,EAE5C,KAEN,IAAIgC,EACTwB,EACAxI,EACiB,UAAjBuI,EACAtG,CAAQ,CAEX,EAGyDtG,CAAO,EAKjE,OAJAyK,EAAY5B,QAAUA,EACtB4B,EAAYvW,KAAOA,EAAK4R,aACxB2E,EAAY1B,iBAAmBA,GAAoB,KACnD0B,EAAY3B,aAAegB,EAAmB9Y,CAAQ,EAC/CyZ,CACT,CC3iBa,IAAAqC,GAAY,CAavBC,cAAe,eACf,EA0BWC,EAAY,CAEvBC,QAAS,UAGTC,OAAQ,SAGRC,QAAS,UAGTvV,SAAU,WAGVwV,MAAO,OACE,EAEL,SAAUC,GACdC,GAEA,OAAQA,GACN,IAA+B,UAC/B,IAA+B,UAC/B,IAAA,YACE,OAAON,EAAUC,QACnB,IAAA,SACE,OAAOD,EAAUE,OACnB,IAAA,UACE,OAAOF,EAAUG,QACnB,IAAA,WACE,OAAOH,EAAUpV,SAGnB,QAEE,OAAOoV,EAAUI,KACpB,CACH,OCvCaG,GAKXtd,YACEud,EACAzS,EACA0S,GAEA,IAOQC,EhB7DU,YAAb,OgBuDQF,GAA4B,MAATzS,GAA6B,MAAZ0S,GAE/C/f,KAAKigB,KAAOH,EACZ9f,KAAKqN,MAAQA,MAAAA,EAAAA,EAAS8G,KAAAA,EACtBnU,KAAK+f,SAAWA,MAAAA,EAAAA,EAAY5L,KAAAA,IAO5BnU,KAAKigB,MALCD,EAAWF,GAKIG,KACrBjgB,KAAKqN,MAAQ2S,EAAS3S,MACtBrN,KAAK+f,SAAWC,EAASD,SAE5B,CACF,CCzEK,SAAUG,EAAMC,GACpB,MAAO,IAAIC,KAET7S,QAAQ8C,QAAO,EAAGe,KAAK,IAAM+O,EAAE,GAAGC,CAAa,CAAC,CAClD,CACF,OCsHaC,iBAzGX9d,cAFUvC,KAAKsgB,MAAY,CAAA,EAGzBtgB,KAAKugB,KAAO,IAAIC,eAChBxgB,KAAKygB,QAAO,EACZzgB,KAAK0gB,WAAahX,EAAU8H,SAC5BxR,KAAK2gB,aAAe,IAAIpT,QAAQ8C,IAC9BrQ,KAAKugB,KAAK5Z,iBAAiB,QAAS,KAClC3G,KAAK0gB,WAAahX,EAAUiI,MAC5BtB,GACF,CAAC,EACDrQ,KAAKugB,KAAK5Z,iBAAiB,QAAS,KAClC3G,KAAK0gB,WAAahX,EAAUkX,cAC5BvQ,GACF,CAAC,EACDrQ,KAAKugB,KAAK5Z,iBAAiB,OAAQ,KACjC0J,GACF,CAAC,CACH,CAAC,CACF,CAIDc,KACEpF,EACAiP,EACAhL,EACAxJ,EACA2U,GAEA,GAAInb,KAAKsgB,MACP,MAAMxV,EAAc,+BAA+B,EAOrD,GALIjI,EAAmBkJ,CAAG,GAAKiE,IAC7BhQ,KAAKugB,KAAKM,gBAAkB,CAAA,GAE9B7gB,KAAKsgB,MAAQ,CAAA,EACbtgB,KAAKugB,KAAKO,KAAK9F,EAAQjP,EAAK,CAAA,CAAI,EAChBoI,KAAAA,IAAZgH,EACF,IAAK,IAAM1X,KAAO0X,EACZA,EAAQvM,eAAenL,CAAG,GAC5BzD,KAAKugB,KAAKQ,iBAAiBtd,EAAK0X,EAAQ1X,GAAK6Z,SAAQ,CAAE,EAS7D,OALanJ,KAAAA,IAAT3N,EACFxG,KAAKugB,KAAKpP,KAAK3K,CAAI,EAEnBxG,KAAKugB,KAAKpP,OAELnR,KAAK2gB,YACb,CAEDpP,eACE,GAAKvR,KAAKsgB,MAGV,OAAOtgB,KAAK0gB,WAFV,MAAM5V,EAAc,uCAAuC,CAG9D,CAED2G,YACE,GAAI,CAACzR,KAAKsgB,MACR,MAAMxV,EAAc,oCAAoC,EAE1D,IACE,OAAO9K,KAAKugB,KAAKhX,MAGlB,CAFC,MAAOsC,GACP,MAAO,CAAC,CACT,CACF,CAEDoG,cACE,GAAKjS,KAAKsgB,MAGV,OAAOtgB,KAAKugB,KAAKS,SAFf,MAAMlW,EAAc,sCAAsC,CAG7D,CAEDqH,eACE,GAAKnS,KAAKsgB,MAGV,OAAOtgB,KAAKugB,KAAKU,WAFf,MAAMnW,EAAc,uCAAuC,CAG9D,CAGDiJ,QACE/T,KAAKugB,KAAKxM,OACX,CAEDiK,kBAAkBkD,GAChB,OAAOlhB,KAAKugB,KAAKvC,kBAAkBkD,CAAM,CAC1C,CAEDhQ,0BAA0BiQ,GACA,MAApBnhB,KAAKugB,KAAKa,QACZphB,KAAKugB,KAAKa,OAAOza,iBAAiB,WAAYwa,CAAQ,CAEzD,CAED9P,6BAA6B8P,GACH,MAApBnhB,KAAKugB,KAAKa,QACZphB,KAAKugB,KAAKa,OAAOC,oBAAoB,WAAYF,CAAQ,CAE5D,CACF,EAGCV,UACEzgB,KAAKugB,KAAKe,aAAe,MAC1B,CACF,CAEe,SAAAC,IACd,OAAqD,IAAIlB,EAC3D,OCzFamB,GAsCXC,8BACE,OAAOzhB,KAAK0hB,UAAY1hB,KAAK2hB,YAC9B,CAODpf,YAAYqf,EAAgBvK,EAAeuB,EAA4B,MAjCvE5Y,KAAY6hB,aAAW,EACf7hB,KAAkB8hB,mBAAY,CAAA,EAC9B9hB,KAAoB+hB,qBAAY,CAAA,EAChC/hB,KAAUgiB,WAAuD,GAMjEhiB,KAAMiiB,OAAkB9N,KAAAA,EACxBnU,KAAUkiB,WAAY/N,KAAAA,EACtBnU,KAAQmiB,SAAsBhO,KAAAA,EAC9BnU,KAAgBoiB,iBAAW,EAG3BpiB,KAAQqiB,SAAsClO,KAAAA,EAC9CnU,KAAOsiB,QAAgCnO,KAAAA,EAkB7CnU,KAAKuiB,KAAOX,EACZ5hB,KAAKwiB,MAAQnL,EACbrX,KAAKuZ,UAAYX,EACjB5Y,KAAKyiB,UAAYtJ,IACjBnZ,KAAK0iB,WAAa1iB,KAAK2iB,mBAAmB3iB,KAAKwiB,KAAK,EACpDxiB,KAAK4iB,OAAM,UACX5iB,KAAK6iB,cAAgBxV,IAGnB,GAFArN,KAAKmiB,SAAWhO,KAAAA,EAChBnU,KAAKoiB,iBAAmB,EACpB/U,EAAM7D,YAAYC,EAAiBS,QAAQ,EAC7ClK,KAAK8hB,mBAAqB,CAAA,EAC1B9hB,KAAK8iB,qBAAoB,MACpB,CACL,IAAMC,EAAiB/iB,KAAKyhB,8BAC5B,GAAI5S,GAAkBxB,EAAM9D,OAAQ,EAAE,EAAG,CACvC,GAAIwZ,CAAAA,EASF,OANA/iB,KAAK0hB,UAAYjO,KAAKuP,IACH,EAAjBhjB,KAAK0hB,UzBrF0B,GyBsFF,EAE/B1hB,KAAK8hB,mBAAqB,CAAA,EAJ1B9hB,KAKAA,KAAK8iB,qBAAoB,EAPzBzV,EAAQtD,EAAkB,CAU7B,CACD/J,KAAKiiB,OAAS5U,EACdrN,KAAKijB,YAAW,QACjB,CACH,EACAjjB,KAAKkjB,sBAAwB7V,IAC3BrN,KAAKmiB,SAAWhO,KAAAA,EACZ9G,EAAM7D,YAAYC,EAAiBS,QAAQ,EAC7ClK,KAAK8iB,qBAAoB,GAEzB9iB,KAAKiiB,OAAS5U,EACdrN,KAAKijB,YAAW,SAEpB,EACAjjB,KAAK0hB,UAAY,EACjB1hB,KAAK2hB,aAAe3hB,KAAKuiB,KAAKY,QAAQzF,mBACtC1d,KAAKojB,SAAW,IAAI7V,QAAQ,CAAC8C,EAAS7C,KACpCxN,KAAKqiB,SAAWhS,EAChBrQ,KAAKsiB,QAAU9U,EACfxN,KAAKqjB,OAAM,CACb,CAAC,EAIDrjB,KAAKojB,SAAShS,KAAK,KAAM,MAAQ,CAClC,CAEOkS,wBACN,IAAMC,EAAavjB,KAAK6hB,aACxB,OAAO9Q,GAAU/Q,KAAKwjB,gBAAgBD,EAAaxS,CAAM,CAC1D,CAEO4R,mBAAmBtL,GACzB,OAAqB,OAAdA,EAAKV,MACb,CAEO0M,SACS,YAAXrjB,KAAK4iB,QAIazO,KAAAA,IAAlBnU,KAAKmiB,WAGLniB,KAAK0iB,WACiBvO,KAAAA,IAApBnU,KAAKkiB,WACPliB,KAAKyjB,iBAAgB,EAEjBzjB,KAAK8hB,mBACP9hB,KAAK0jB,aAAY,EAEb1jB,KAAK+hB,qBAEP/hB,KAAK2jB,eAAc,EAEnB3jB,KAAK4jB,eAAiB1Q,WAAW,KAC/BlT,KAAK4jB,eAAiBzP,KAAAA,EACtBnU,KAAK6jB,gBAAe,CACtB,EAAG7jB,KAAK0hB,SAAS,EAKvB1hB,KAAK8jB,eAAc,EAEtB,CAEOC,cACNhb,GAGAwE,QAAQyW,IAAI,CACVhkB,KAAKuiB,KAAKY,QAAQc,cAAe,EACjCjkB,KAAKuiB,KAAKY,QAAQe,kBAAmB,EACtC,EAAE9S,KAAK,CAAA,CAAE+S,EAAWC,MACnB,OAAQpkB,KAAK4iB,QACX,IAAA,UACE7Z,EAASob,EAAWC,CAAa,EACjC,MACF,IAAA,YACEpkB,KAAKijB,YAAW,YAChB,MACF,IAAA,UACEjjB,KAAKijB,YAAW,SAGnB,CACH,CAAC,CACF,CAIOQ,mBACNzjB,KAAK+jB,cAAc,CAACI,EAAWC,KAC7B,IAAMrH,EAAckB,GAClBje,KAAKuiB,KAAKY,QACVnjB,KAAKuiB,KAAK8B,UACVrkB,KAAKyiB,UACLziB,KAAKwiB,MACLxiB,KAAKuZ,SAAS,EAEV+K,EAAgBtkB,KAAKuiB,KAAKY,QAAQoB,aACtCxH,EACAwE,EACA4C,EACAC,CAAa,GAEfpkB,KAAKmiB,SAAWmC,GACF7W,WAAU,EAAG2D,KAAK,IAC9BpR,KAAKmiB,SAAWhO,KAAAA,EAChBnU,KAAKkiB,WAAanW,EAClB/L,KAAK8hB,mBAAqB,CAAA,EAC1B9hB,KAAK8iB,qBAAoB,CAC3B,EAAG9iB,KAAK6iB,aAAa,CACvB,CAAC,CACF,CAEOa,eAEN,IAAM3X,EAAM/L,KAAKkiB,WACjBliB,KAAK+jB,cAAc,CAACI,EAAWC,KAC7B,IAAMrH,EAAcwB,GAClBve,KAAKuiB,KAAKY,QACVnjB,KAAKuiB,KAAK8B,UACVtY,EACA/L,KAAKwiB,KAAK,EAENgC,EAAgBxkB,KAAKuiB,KAAKY,QAAQoB,aACtCxH,EACAwE,EACA4C,EACAC,CAAa,GAEfpkB,KAAKmiB,SAAWqC,GACF/W,WAAU,EAAG2D,KAAK7H,IAE9BvJ,KAAKmiB,SAAWhO,KAAAA,EAChBnU,KAAKwjB,gBAAgBja,EAAOqU,OAAO,EACnC5d,KAAK8hB,mBAAqB,CAAA,EACtBvY,EAAOsU,YACT7d,KAAK+hB,qBAAuB,CAAA,GAE9B/hB,KAAK8iB,qBAAoB,CAC3B,EAAG9iB,KAAK6iB,aAAa,CACvB,CAAC,CACF,CAEOgB,kBACN,IAAMlF,ELiNyC,OKjNC3e,KAAKoiB,iBAC/C7Y,EAAS,IAAIoU,EACjB3d,KAAK6hB,aACL7hB,KAAKwiB,MAAM7L,KAAI,CAAE,EAIb5K,EAAM/L,KAAKkiB,WACjBliB,KAAK+jB,cAAc,CAACI,EAAWC,KAC7BjlB,IAAI4d,EACJ,IACEA,EAAc2B,GACZ1e,KAAKuiB,KAAK8B,UACVrkB,KAAKuiB,KAAKY,QACVpX,EACA/L,KAAKwiB,MACL7D,EACA3e,KAAKyiB,UACLlZ,EACAvJ,KAAKsjB,sBAAqB,CAAE,CAM/B,CAJC,MAAOzX,GAGP,OAFA7L,KAAKiiB,OAASpW,EAAd7L,KACAA,KAAKijB,YAAW,QAEjB,CACD,IAAMwB,EAAgBzkB,KAAKuiB,KAAKY,QAAQoB,aACtCxH,EACAwE,EACA4C,EACAC,EACW,CAAA,IAEbpkB,KAAKmiB,SAAWsC,GACFhX,WAAU,EAAG2D,KAAK,IAC9BpR,KAAK0kB,oBAAmB,EACxB1kB,KAAKmiB,SAAWhO,KAAAA,EAChBnU,KAAKwjB,gBAAgBmB,EAAU/G,OAAO,EAClC+G,EAAU9G,WACZ7d,KAAKuZ,UAAYoL,EAAU/L,SAC3B5Y,KAAKijB,YAAW,YAEhBjjB,KAAK8iB,qBAAoB,CAE7B,EAAG9iB,KAAK6iB,aAAa,CACvB,CAAC,CACF,CAEO6B,sBAIY,GL6J6B,OKhKG1kB,KAAKoiB,kBAGjC,WACpBpiB,KAAKoiB,kBAAoB,EAE5B,CAEOuB,iBACN3jB,KAAK+jB,cAAc,CAACI,EAAWC,KAC7B,IAAMrH,EAAcF,GAClB7c,KAAKuiB,KAAKY,QACVnjB,KAAKuiB,KAAK8B,UACVrkB,KAAKyiB,SAAS,EAEVmC,EAAkB5kB,KAAKuiB,KAAKY,QAAQoB,aACxCxH,EACAwE,EACA4C,EACAC,CAAa,GAEfpkB,KAAKmiB,SAAWyC,GACAnX,WAAU,EAAG2D,KAAKwH,IAChC5Y,KAAKmiB,SAAWhO,KAAAA,EAChBnU,KAAKuZ,UAAYX,EACjB5Y,KAAKijB,YAAW,UAClB,EAAGjjB,KAAKkjB,qBAAqB,CAC/B,CAAC,CACF,CAEOY,iBACN9jB,KAAK+jB,cAAc,CAACI,EAAWC,KAC7B,IAAMrH,EAAcI,GAClBnd,KAAKuiB,KAAKY,QACVnjB,KAAKuiB,KAAK8B,UACVrkB,KAAKyiB,UACLziB,KAAKwiB,MACLxiB,KAAKuZ,SAAS,EAEVsL,EAAmB7kB,KAAKuiB,KAAKY,QAAQoB,aACzCxH,EACAwE,EACA4C,EACAC,CAAa,GAEfpkB,KAAKmiB,SAAW0C,GACCpX,WAAU,EAAG2D,KAAKwH,IACjC5Y,KAAKmiB,SAAWhO,KAAAA,EAChBnU,KAAKuZ,UAAYX,EACjB5Y,KAAKwjB,gBAAgBxjB,KAAKwiB,MAAM7L,KAAM,CAAA,EACtC3W,KAAKijB,YAAW,UAClB,EAAGjjB,KAAK6iB,aAAa,CACvB,CAAC,CACF,CAEOW,gBAAgBsB,GACtB,IAAMC,EAAM/kB,KAAK6hB,aACjB7hB,KAAK6hB,aAAeiD,EAKhB9kB,KAAK6hB,eAAiBkD,GACxB/kB,KAAKglB,iBAAgB,CAExB,CAEO/B,YAAYrD,GAClB,GAAI5f,KAAK4iB,SAAWhD,EAGpB,OAAQA,GACN,IAAiC,YACjC,IAAA,UAIE5f,KAAK4iB,OAAShD,EACQzL,KAAAA,IAAlBnU,KAAKmiB,SACPniB,KAAKmiB,SAASzU,SACL1N,KAAK4jB,iBACdvQ,aAAarT,KAAK4jB,cAAc,EAChC5jB,KAAK4jB,eAAiBzP,KAAAA,EACtBnU,KAAK8iB,qBAAoB,GAE3B,MACF,IAAA,UAIE,IAAMmC,EAAqD,WAAzCjlB,KAAK4iB,OACvB5iB,KAAK4iB,OAAShD,EACVqF,IACFjlB,KAAKglB,iBAAgB,EACrBhlB,KAAKqjB,OAAM,GAEb,MACF,IAAA,SAGErjB,KAAK4iB,OAAShD,EACd5f,KAAKglB,iBAAgB,EACrB,MACF,IAAA,WAIEhlB,KAAKiiB,OAAShY,IACdjK,KAAK4iB,OAAShD,EACd5f,KAAKglB,iBAAgB,EACrB,MACF,IAAA,QAQA,IAAA,UAKEhlB,KAAK4iB,OAAShD,EACd5f,KAAKglB,iBAAgB,CAGxB,CACF,CAEOlC,uBACN,OAAQ9iB,KAAK4iB,QACX,IAAA,UACE5iB,KAAKijB,YAAW,UAChB,MACF,IAAA,YACEjjB,KAAKijB,YAAW,YAChB,MACF,IAAA,UACEjjB,KAAKqjB,OAAM,CAKd,CACF,CAKD6B,eACE,IAAMC,EAAgBxF,GAA+B3f,KAAK4iB,MAAM,EAChE,MAAO,CACLwC,iBAAkBplB,KAAK6hB,aACvBwD,WAAYrlB,KAAKwiB,MAAM7L,KAAM,EAC7BiJ,MAAOuF,EACPvM,SAAU5Y,KAAKuZ,UACf+L,KAAMtlB,KACN4hB,IAAK5hB,KAAKuiB,KAEb,CAmBDgD,GACEnd,EACA0X,EAIAzS,EACAmY,GAGA,IAAMxF,EAAW,IAAIH,GAClBC,GAEkC3L,KAAAA,EACnC9G,GAAS8G,KAAAA,EACTqR,GAAarR,KAAAA,CAAS,EAGxB,OADAnU,KAAKylB,aAAazF,CAAQ,EACnB,KACLhgB,KAAK0lB,gBAAgB1F,CAAQ,CAC/B,CACD,CAQD5O,KACEuU,EACAC,GAIA,OAAO5lB,KAAKojB,SAAShS,KACnBuU,EACAC,CAAyD,CAE5D,CAKDC,MAASD,GACP,OAAO5lB,KAAKoR,KAAK,KAAMwU,CAAU,CAClC,CAKOH,aAAazF,GACnBhgB,KAAKgiB,WAAW5gB,KAAK4e,CAAQ,EAC7BhgB,KAAK8lB,gBAAgB9F,CAAQ,CAC9B,CAKO0F,gBAAgB1F,GACtB,IAAM3gB,EAAIW,KAAKgiB,WAAW/S,QAAQ+Q,CAAQ,EAChC,CAAC,IAAP3gB,GACFW,KAAKgiB,WAAW+D,OAAO1mB,EAAG,CAAC,CAE9B,CAEO2lB,mBACNhlB,KAAKgmB,eAAc,EACDhmB,KAAKgiB,WAAWnV,MAAK,EAC7BqL,QAAQ8H,IAChBhgB,KAAK8lB,gBAAgB9F,CAAQ,CAC/B,CAAC,CACF,CAEOgG,iBACN,GAAsB7R,KAAAA,IAAlBnU,KAAKqiB,SAAwB,CAC/BljB,IAAI8mB,EAAY,CAAA,EAChB,OAAQtG,GAA+B3f,KAAK4iB,MAAM,GAChD,KAAKtD,EAAUG,QACbyG,EAASlmB,KAAKqiB,SAAS8D,KAAK,KAAMnmB,KAAKklB,QAAQ,CAAC,IAChD,MACF,KAAK5F,EAAUpV,SACf,KAAKoV,EAAUI,MAEbwG,EADelmB,KAAKsiB,QACJ6D,KAAK,KAAMnmB,KAAKiiB,MAAsB,CAAC,IACvD,MACF,QACEgE,EAAY,CAAA,CAEf,CACGA,IACFjmB,KAAKqiB,SAAWlO,KAAAA,EAChBnU,KAAKsiB,QAAUnO,KAAAA,EAElB,CACF,CAEO2R,gBAAgB9F,GAEtB,OADsBL,GAA+B3f,KAAK4iB,MAAM,GAE9D,KAAKtD,EAAUC,QACf,KAAKD,EAAUE,OACTQ,EAASC,MACXiG,EAASlG,EAASC,KAAKkG,KAAKnG,EAAUhgB,KAAKklB,QAAQ,CAAC,IAEtD,MACF,KAAK5F,EAAUG,QACTO,EAASD,UACXmG,EAASlG,EAASD,SAASoG,KAAKnG,CAAQ,CAAC,EAAC,EAE5C,MACF,KAAKV,EAAUpV,SACf,KAAKoV,EAAUI,MAOf,QAEMM,EAAS3S,OACX6Y,EACElG,EAAS3S,MAAM8Y,KAAKnG,EAAUhgB,KAAKiiB,MAAsB,CAAC,GAGjE,CACF,CAMDmE,SACE,IAAMC,EACoC,WAAxCrmB,KAAK4iB,QACM,YAAX5iB,KAAK4iB,OAIP,OAHIyD,GACFrmB,KAAKijB,YAAW,WAEXoD,CACR,CAMDC,QACE,IAAMD,EAAkD,YAA1CrmB,KAAK4iB,OAInB,OAHIyD,GACFrmB,KAAKijB,YAAW,WAEXoD,CACR,CAOD3Y,SACE,IAAM2Y,EACqC,YAAzCrmB,KAAK4iB,QACM,YAAX5iB,KAAK4iB,OAIP,OAHIyD,GACFrmB,KAAKijB,YAAW,aAEXoD,CACR,CACF,OC/mBYE,EAGXhkB,YACUikB,EACRljB,GADQtD,KAAQwmB,SAARA,EAGJljB,aAAoB0H,EACtBhL,KAAKqkB,UAAY/gB,EAEjBtD,KAAKqkB,UAAYrZ,EAASY,YAAYtI,EAAUkjB,EAAS1jB,IAAI,CAEhE,CAODwa,WACE,MAAO,QAAUtd,KAAKqkB,UAAUpZ,OAAS,IAAMjL,KAAKqkB,UAAUnZ,IAC/D,CAESub,QACRnf,EACAhE,GAEA,OAAO,IAAIijB,EAAUjf,EAAShE,CAAQ,CACvC,CAKDojB,WACE,IAAMpjB,EAAW,IAAI0H,EAAShL,KAAKqkB,UAAUpZ,OAAQ,EAAE,EACvD,OAAOjL,KAAKymB,QAAQzmB,KAAKwmB,SAAUljB,CAAQ,CAC5C,CAKD2H,aACE,OAAOjL,KAAKqkB,UAAUpZ,MACvB,CAKDuO,eACE,OAAOxZ,KAAKqkB,UAAUnZ,IACvB,CAMD1I,WACE,OAAOiW,GAAczY,KAAKqkB,UAAUnZ,IAAI,CACzC,CAKDiY,cACE,OAAOnjB,KAAKwmB,QACb,CAMDG,aACE,IV9GmBzb,EUkHb5H,EAJAsjB,EV7GY,KADC1b,EU8GIlL,KAAKqkB,UAAUnZ,MV7G/B5L,OACA,KAGK,CAAC,KADT6Y,EAAQjN,EAAKwN,YAAY,GAAG,GAEzB,GAEOxN,EAAK2B,MAAM,EAAGsL,CAAK,EUuGjC,OAAgB,OAAZyO,EACK,MAEHtjB,EAAW,IAAI0H,EAAShL,KAAKqkB,UAAUpZ,OAAQ2b,CAAO,EACrD,IAAIL,EAAUvmB,KAAKwmB,SAAUljB,CAAQ,EAC7C,CAKDujB,aAAarkB,GACX,GAA4B,KAAxBxC,KAAKqkB,UAAUnZ,KACjB,MAAMT,GAAqBjI,CAAI,CAElC,CACF,CA0LK,SAAUskB,GAAQlF,GACtB,IAAMmF,EAA0B,CAC9BtM,SAAU,GACVC,MAAO,IAET,OASFwF,eAAe8G,EACbpF,EACAmF,EACAE,GAEA,IAAMC,EAAmB,CAEvBD,UAAAA,GAEF,IAAME,EAAWC,MAAMC,GAAKzF,EAAKsF,CAAG,EACpCH,EAAYtM,SAASrZ,KAAK,GAAG+lB,EAAS1M,QAAQ,EAC9CsM,EAAYrM,MAAMtZ,KAAK,GAAG+lB,EAASzM,KAAK,EACV,MAA1ByM,EAASxM,eACXyM,MAAMJ,EAAcpF,EAAKmF,EAAaI,EAASxM,aAAa,CAEhE,EAxBuBiH,EAAKmF,CAAW,EAAE3V,KAAK,IAAM2V,CAAW,CAC/D,CA+CgB,SAAAM,GACdzF,EACA0F,GAEe,MAAXA,GACgC,UAA9B,OAAOA,EAAQC,YACjBvZ,EACE,qBACgB,EACA,IAChBsZ,EAAQC,UAAU,EAIxB,INpOAjkB,EACAkkB,EACAP,EACAM,EAkBMxb,EM+MA0b,EAAKH,GAAW,GAChBvK,GNtONzV,EMuOEsa,EAAIuB,QNtON7f,EMuOEse,EAAIyC,UNtONmD,EMuOkB,INtOlBP,EMuOEQ,EAAGR,UNtOLM,EMuOEE,EAAGF,WNrOCrM,EAAuB,GACzB5X,EAAS8H,OACX8P,EAAkB,OAAI,GAEtBA,EAAkB,OAAI5X,EAAS4H,KAAO,IAEpCsc,GAAgC,EAAnBA,EAAUloB,SACzB4b,EAAqB,UAAIsM,GAEvBP,IACF/L,EAAqB,UAAI+L,GAEvBM,IACFrM,EAAsB,WAAIqM,GAGtBxb,EAAMqC,EAAQC,EADJ/K,EAASkI,sBACIlE,EAAQxE,KAAMwE,EAAQyU,SAAS,EAEtDzJ,EAAUhL,EAAQwV,uBAOxBC,EANoB,IAAIhC,EACtBhP,EAHa,MAKb6P,GAAYtU,EAAShE,EAAS2H,MAAM,EACpCqH,CAAO,GAEG4I,UAAYA,EACxB6B,EAAY3B,aAAegB,EAAmB9Y,CAAQ,EAC/CyZ,GM4MP,OAAO6E,EAAIuB,QAAQuE,sBAAsB3K,EAAawE,CAAiB,CACzE,CA8BgB,SAAAoG,GACd/F,EACAhJ,GAEAgJ,EAAIiF,aAAa,gBAAgB,ENjMjCvf,EMmMEsa,EAAIuB,QNlMN7f,EMmMEse,EAAIyC,UNlMNzL,EMmMEA,ENlMFQ,EMmMED,EAAW,ENhMPpN,EAAMqC,EADI9K,EAAS+H,gBACI/D,EAAQxE,KAAMwE,EAAQyU,SAAS,EAEtDvV,EAAO2T,GAAiBvB,EAAUQ,CAAQ,EAE1C9G,EAAUhL,EAAQwV,uBAOxBC,EANoB,IAAIhC,EACtBhP,EALa,QAOb0P,EAAgBnU,EAAS8R,CAAQ,EACjC9G,CAAO,GAEG6I,QARI,CAAEmD,eAAgB,mCASlCvB,EAAYvW,KAAOA,EACnBuW,EAAY3B,aAAesB,EAAmBpZ,CAAQ,EM+KtD,INlMAgE,EACAhE,EAEA8V,EAGMrN,EAEAvF,EM0LAuW,EN9KCA,EMoLP,OAAO6E,EAAIuB,QAAQuE,sBAAsB3K,EAAawE,CAAiB,CACzE,CAQM,SAAUqG,GAAehG,GAC7BA,EAAIiF,aAAa,gBAAgB,ENrOjCvf,EMuOEsa,EAAIuB,QNtON7f,EMuOEse,EAAIyC,UNtONjL,EMuOED,EAAW,ENpOPpN,EAAMqC,EADI9K,EAAS+H,gBACI/D,EAAQxE,KAAMwE,EAAQyU,SAAS,EAEtDzJ,EAAUhL,EAAQwV,uBAOxBC,EANoB,IAAIhC,EACtBhP,EAHa,MAKb8P,GAAmBvU,EAAS8R,CAAQ,EACpC9G,CAAO,GAEG8I,aAAesB,EAAmBpZ,CAAQ,EMwNtD,INtOAgE,EACAhE,EACA8V,EAGMrN,EMiOAgR,ENvNCA,EM4NP,OAAO6E,EAAIuB,QACRuE,sBAAsB3K,EAAawE,CAAiB,EACpDnQ,KAAKrF,IACJ,GAAY,OAARA,EACF,MzBxNC,IAAI7C,EACTO,EAAiBoe,gBACjB,iDAAiD,EyBwN/C,OAAO9b,CACT,CAAC,CACL,CAQM,SAAU+b,GAAalG,GAC3BA,EAAIiF,aAAa,cAAc,ENjN/Bvf,EMkNyCsa,EAAIuB,QN9MvCpX,EAAMqC,GAHZ9K,EMiNsDse,EAAIyC,WN/MjChZ,gBACI/D,EAAQxE,KAAMwE,EAAQyU,SAAS,EAEtDzJ,EAAUhL,EAAQwV,uBAGlBC,EAAc,IAAIhC,EAAYhP,EAJrB,SAGf,SAAiBgc,EAA0BC,KACe1V,CAAO,GACrDgJ,aAAe,CAAC,IAAK,KACjCyB,EAAY3B,aAAesB,EAAmBpZ,CAAQ,EMuMtD,INlNAgE,EACAhE,EAGMyI,EM8MAgR,ENtMCA,EMuMP,OAAO6E,EAAIuB,QAAQuE,sBAAsB3K,EAAawE,CAAiB,CACzE,CAYgB,SAAA0G,GAAUrG,EAAgBsG,GVjdpBhd,EUkdE0W,EAAIyC,UAAUnZ,KVjd9Bid,EUidoCD,EVhdvCjM,MAAM,GAAG,EACTmM,OAAOC,GAAgC,EAAnBA,EAAU/oB,MAAU,EACxC+B,KAAK,GAAG,EU8cX,IVldoB6J,EUkdd0b,EV7cc,IAAhB1b,EAAK5L,OACA6oB,EAEAjd,EAAO,IAAMid,EU2chB7kB,EAAW,IAAI0H,EAAS4W,EAAIyC,UAAUpZ,OAAQ2b,CAAO,EAC3D,OAAO,IAAIL,EAAU3E,EAAIuB,QAAS7f,CAAQ,CAC5C,CCrbA,SAASglB,GACP1G,EACA1W,GAEA,GAAI0W,aAAe2G,GAAqB,CACtC,IAAMjhB,EAAUsa,EAChB,GAAuB,MAAnBta,EAAQkhB,QACV,M1B8JG,IAAItf,EACTO,EAAiBgf,kBACjB,6CAEExf,EACA,uCAAuC,E0BjKnC4R,EAAY,IAAI0L,EAAUjf,EAASA,EAAQkhB,OAAQ,EACzD,OAAY,MAARtd,EACKod,GAAYzN,EAAW3P,CAAI,EAE3B2P,CAEV,CAEC,OAAa1G,KAAAA,IAATjJ,EACK+c,GAAUrG,EAAK1W,CAAI,EAEnB0W,CAGb,CAqBgB,SAAAA,GACd8G,EACAC,GAEA,GAAIA,GA9DG,kBAAkBC,KA8DFD,CA9DqB,EA8DT,CACjC,GAAID,aAAwBH,GAC1B,OA1DcjhB,EA0DIohB,EA1D0B3c,EA0DZ4c,EAzD7B,IAAIpC,EAAUjf,EAASyE,CAAG,EA2D7B,MAAM1B,EACJ,0EAA0E,CAG/E,CACC,OAAOie,GAAYI,EAAcC,CAAS,EAjE9C,IAAoBrhB,EAA8ByE,CAmElD,CAEA,SAAS8c,GACP/lB,EACAgmB,GAEA,IAAMpd,EAAeod,MAAAA,EAAM,KAAA,EAANA,EAAS7f,GAC9B,OAAoB,MAAhByC,EACK,KAEFV,EAASS,mBAAmBC,EAAc5I,CAAI,CACvD,CAEM,SAAUimB,GACd5F,EACArgB,EACAkmB,EACA1B,EAEI,IAEJnE,EAAQrgB,KAAUA,EAAH,IAAWkmB,EAC1B,IAAMC,EAASpmB,EAAmBC,CAAI,EAQ9BomB,GANJD,KhCzHC/I,MAA0BiJ,IAChB/B,MAAMgC,MAAMD,EAAU,CACnCE,YAAa,SACd,CAAA,GACaC,egCsHenG,EAAQrgB,QAAQ,EAC3CI,EAAqB,UAAW,CAAA,CAAI,GAEtCigB,EAAQoG,iBAAmB,CAAA,EAC3BpG,EAAQpH,UAAYkN,EAAS,QAAU,OACb3B,GAAH,cACnB4B,IACF/F,EAAQqG,mBACmB,UAAzB,OAAON,EACHA,G/BlEM,CACd/M,EACAsN,KAEA,GAAItN,EAAMuN,IACR,MAAM,IAAIjpB,MACR,8GAA8G,EAIlH,IAKMkpB,EAAUF,GAAa,eACvBG,EAAMzN,EAAMyN,KAAO,EACnBC,EAAM1N,EAAM0N,KAAO1N,EAAM2N,QAC/B,GAAKD,EAwBL,OApBME,EAAOlmB,OAAAqZ,OAAA,CAEX8M,IAAK,kCAAkCL,EACvCM,IAAKN,EACLC,IAAAA,EACAM,IAAKN,EAAM,KACXO,UAAWP,EACXC,IAAAA,EACAC,QAASD,EACTO,SAAU,CACRC,iBAAkB,SAClBC,WAAY,GAIX,EAAAnO,CAAK,EAKH,CACLxZ,EAA8B4V,KAAK6B,UAjCtB,CACbmQ,IAAK,OACLniB,KAAM,KACP,CA8BoD,CAAC,EACpDzF,EAA8B4V,KAAK6B,UAAU2P,CAAO,CAAC,EAHrC,IAKhB1oB,KAAK,GAAG,EA3BR,MAAM,IAAIZ,MAAM,sDAAsD,CA4B1E,G+BoB8ByoB,EAAe/F,EAAQqH,IAAIlD,QAAQmC,SAAS,EAE1E,OAQalB,GAgBXhmB,YAIWioB,EACAC,EAIAC,EAIAC,EACAC,EACFrB,EAAmB,CAAA,GAXjBvpB,KAAGwqB,IAAHA,EACAxqB,KAAayqB,cAAbA,EAIAzqB,KAAiB0qB,kBAAjBA,EAIA1qB,KAAI2qB,KAAJA,EACA3qB,KAAgB4qB,iBAAhBA,EACF5qB,KAAgBupB,iBAAhBA,EA9BTvpB,KAAOwoB,QAAoB,KAMnBxoB,KAAK6qB,MAAW7hB,EACxBhJ,KAAS+b,UAAW,QACD/b,KAAM8qB,OAAkB,KAEnC9qB,KAAQ+qB,SAAY,CAAA,EAsB1B/qB,KAAKgrB,uB3B1KuC,K2B2K5ChrB,KAAKirB,oB3BpKoC,I2BqKzCjrB,KAAKkrB,UAAY,IAAIC,IAEnBnrB,KAAKwoB,QADK,MAARmC,EACa3f,EAASS,mBAAmBkf,EAAM3qB,KAAK6qB,KAAK,EAE5ChC,GAAc7oB,KAAK6qB,MAAO7qB,KAAKwqB,IAAIlD,OAAO,CAE5D,CAMDxkB,WACE,OAAO9C,KAAK6qB,KACb,CAED/nB,SAASA,GACP9C,KAAK6qB,MAAQ/nB,EACI,MAAb9C,KAAK2qB,KACP3qB,KAAKwoB,QAAUxd,EAASS,mBAAmBzL,KAAK2qB,KAAM7nB,CAAI,EAE1D9C,KAAKwoB,QAAUK,GAAc/lB,EAAM9C,KAAKwqB,IAAIlD,OAAO,CAEtD,CAKD5J,yBACE,OAAO1d,KAAKirB,mBACb,CAEDvN,uBAAuB0N,GACrBpd,EACE,OACe,EACCyL,OAAO4R,kBACvBD,CAAI,EAENprB,KAAKirB,oBAAsBG,CAC5B,CAMDtO,4BACE,OAAO9c,KAAKgrB,sBACb,CAEDlO,0BAA0BsO,GACxBpd,EACE,OACe,EACCyL,OAAO4R,kBACvBD,CAAI,EAENprB,KAAKgrB,uBAAyBI,CAC/B,CAEDnH,sBACE,GAAIjkB,KAAKwpB,mBACP,OAAOxpB,KAAKwpB,mBAEd,IAAM8B,EAAOtrB,KAAKyqB,cAAcc,aAAa,CAAEC,SAAU,CAAA,CAAI,CAAE,EAC/D,GAAIF,EAAM,CACFG,EAAYrE,MAAMkE,EAAKI,WAC7B,GAAkB,OAAdD,EACF,OAAOA,EAAUE,WAEpB,CACD,OAAO,IACR,CAEDzH,0BACE,IAGM0H,EAHN,OAAIC,GAAAA,qBAAqB7rB,KAAKwqB,GAAG,GAAKxqB,KAAKwqB,IAAIsB,SAAS1H,cAC/CpkB,KAAKwqB,IAAIsB,SAAS1H,eAErBwH,EAAW5rB,KAAK0qB,kBAAkBa,aAAa,CAAEC,SAAU,CAAA,CAAI,CAAE,IAEtDpE,MAAMwE,EAASF,YAKhBvP,MAET,IACR,CAKD4P,UAME,OALK/rB,KAAK+qB,WACR/qB,KAAK+qB,SAAW,CAAA,EAChB/qB,KAAKkrB,UAAUhT,QAAQ8T,GAAWA,EAAQte,OAAM,CAAE,EAClD1N,KAAKkrB,UAAUe,SAEV1e,QAAQ8C,SAChB,CAMDwJ,sBAAsBzN,GACpB,OAAO,IAAIma,EAAUvmB,KAAMoM,CAAG,CAC/B,CAMDmY,aACExH,EACAmP,EACA/H,EACAC,EACArU,EAAQ,CAAA,GAER,GAAK/P,KAAK+qB,SAmBR,OAAO,IAAI3d,GAAY7C,GAAU,CAAE,EAnBjB,CACF4hB,ClBjEpBpP,EACAqP,EACAjI,EACAC,EACA8H,EACAG,EACAtc,EAAQ,CAAA,EACRC,EAAkB,CAAA,GkB0DEmc,CACdpP,EACA/c,KAAK8qB,OACL3G,EACAC,EACA8H,EACAlsB,KAAK4qB,iBACL7a,EACA/P,KAAKupB,kBlBhEL5a,EAAYH,GAAgBuO,EAAY7B,SAAS,EACjDnP,EAAMgR,EAAYhR,IAAM4C,EACxBwM,EAAUtX,OAAOqZ,OAAO,GAAIH,EAAY5B,OAAO,EA3BvBA,EA4BdA,GA5BgCiR,EA4BvBA,KA1BvBjR,EAAQ,oBAAsBiR,GAlBhCjR,EA6CeA,EA1CG,QAFlBgJ,EA4CwBA,IA1CqB,EAAnBA,EAAU7kB,SAClC6b,EAAuB,cAAI,YAAcgJ,GAM3CkI,EAoC2BA,EAATlR,EAlCV,8BACN,UAAYkR,MAAAA,EAAAA,EAAmB,cAUjClR,EAwBmBA,EArBG,QAFtBiJ,EAuB4BA,KApB1BjJ,EAAQ,uBAAyBiJ,GkBsE/B,IAAM4H,ElBjDH,IAAI7c,GACTpD,EACAgR,EAAY/B,OACZG,EACA4B,EAAYvW,KACZuW,EAAYzB,aACZyB,EAAYjO,qBACZiO,EAAY9B,QACZ8B,EAAY3B,aACZ2B,EAAYzK,QACZyK,EAAY1B,iBACZ6Q,EACAnc,EACAC,CAAe,EkBoDb,OANAhQ,KAAKkrB,UAAUoB,IAAIN,CAAO,EAE1BA,EAAQve,WAAY,EAAC2D,KACnB,IAAMpR,KAAKkrB,UAAUqB,OAAOP,CAAO,EACnC,IAAMhsB,KAAKkrB,UAAUqB,OAAOP,CAAO,CAAC,EAE/BA,CACR,ClBnFC,IAMJK,EACAtc,EACAC,EAhBAmL,EAkBMxM,EAEAwM,CkB0EL,CAEDuM,4BACE3K,EACAmP,GAEA,GAAM,CAAC/H,EAAWC,GAAiBgD,MAAM7Z,QAAQyW,IAAI,CACnDhkB,KAAKikB,cAAe,EACpBjkB,KAAKkkB,kBAAmB,EACzB,EAED,OAAOlkB,KAAKukB,aACVxH,EACAmP,EACA/H,EACAC,CAAa,EACb3W,YACH,CACF,yCC7Me,SAAA+e,GACd5K,EACAna,EACAmR,GAGA,OADAgJ,EAAM5Z,EAAmB4Z,CAAG,EFsH5Bna,EEnHEA,EFoHFmR,EEnHEA,GFiHFgJ,EEnHEA,GFuHEiF,aAAa,sBAAsB,EAChC,IAAIrF,GAAWI,EAAK,IAAInL,EAAQhP,CAAI,EAAGmR,CAAQ,CEpHxD,CASM,SAAUiE,GAAY+E,GAE1B,OADAA,EAAM5Z,EAAmB4Z,CAAG,GF8OFA,EE7OCA,GF8OvBiF,aAAa,aAAa,EACxB9J,EAAc0P,GAClB7K,EAAIuB,QACJvB,EAAIyC,UACJlL,EAAW,CAAE,EAERyI,EAAIuB,QAAQuE,sBAAsB3K,EAAawE,CAAiB,EAPnE,IAEExE,CE9OR,CAsHgB,SAAA6E,GACd8G,EACAC,GAGA,OAAO+D,GADPhE,EAAe1gB,EAAmB0gB,CAAY,EAG5CC,CAAS,CAEb,CC3QA,SAASgE,GACPC,EACA,CAAEC,mBAAoB9gB,CAAG,GAEzB,IAAMye,EAAMoC,EAAUE,YAAY,KAAK,EAAEvB,aAAY,EAC/CwB,EAAeH,EAAUE,YAAY,eAAe,EACpDE,EAAmBJ,EAAUE,YAAY,oBAAoB,EAEnE,OAAO,IAAIvE,GACTiC,EACAuC,EACAC,EACAjhB,EACAkhB,GAAAA,WAAW,CAEf,CAGEC,sBACE,IAAIhlB,EC5CoB,UD8CtBykB,GAED,UAAChkB,qBAAqB,CAAA,CAAI,CAAC,EAG9BwkB,GAAAA,gBAAgB3qB,GAAM4qB,GAAS,EAAiB,EAEhDD,GAAAA,gBAAgB3qB,GAAM4qB,GAAS,SAAkB,QElDtCC,EAGX9qB,YACW0F,EACAqd,EACA1D,GAFA5hB,KAASiI,UAATA,EACAjI,KAAIslB,KAAJA,EACAtlB,KAAG4hB,IAAHA,CACP,CAEJwD,uBACE,OAAOplB,KAAKiI,UAAUmd,gBACvB,CACDxM,eACE,OAAO5Y,KAAKiI,UAAU2Q,QACvB,CACDgH,YACE,OAAO5f,KAAKiI,UAAU2X,KACvB,CACDyF,iBACE,OAAOrlB,KAAKiI,UAAUod,UACvB,CACF,OCfYiI,GACX/qB,YACW0F,EACQsa,GADRviB,KAASiI,UAATA,EACQjI,KAAIuiB,KAAJA,EAWnBviB,KAAA0N,OAAS1N,KAAKiI,UAAUyF,OAAOyY,KAAKnmB,KAAKiI,SAAS,EAClDjI,KAAA6lB,MAAQ7lB,KAAKiI,UAAU4d,MAAMM,KAAKnmB,KAAKiI,SAAS,EAChDjI,KAAAsmB,MAAQtmB,KAAKiI,UAAUqe,MAAMH,KAAKnmB,KAAKiI,SAAS,EAChDjI,KAAAomB,OAASpmB,KAAKiI,UAAUme,OAAOD,KAAKnmB,KAAKiI,SAAS,CAb9C,CAEJid,eACE,OAAO,IAAImI,EACTrtB,KAAKiI,UAAUid,SACfllB,KACAA,KAAKuiB,IAAI,CAEZ,CAODnR,KACEuU,EACAC,GAEA,OAAO5lB,KAAKiI,UAAUmJ,KAAK8T,IACzB,GAAIS,EACF,OAAOA,EACL,IAAI0H,EAAyBnI,EAAUllB,KAAMA,KAAKuiB,IAAI,CAAC,CAG5D,EAAEqD,CAAU,CACd,CAEDL,GACEnd,EACA0X,EAIAzS,EACAmY,GAEArmB,IAAIouB,EAGuCpZ,KAAAA,EAoB3C,OAnBM2L,IAEFyN,EAD4B,YAA1B,OAAOzN,EACe,GACtBA,EACE,IAAIuN,EAAyBG,EAAcxtB,KAAMA,KAAKuiB,IAAI,CAAC,EAGvC,CACtBtC,KAAQH,EAAeG,KACnB,GACEH,EAAeG,KACb,IAAIoN,EAAyBG,EAAcxtB,KAAMA,KAAKuiB,IAAI,CAAC,EAE/DpO,KAAAA,EACJ4L,SAAUD,EAAeC,UAAY5L,KAAAA,EACrC9G,MAAOyS,EAAezS,OAAS8G,KAAAA,IAI9BnU,KAAKiI,UAAUsd,GACpBnd,EACAmlB,EACAlgB,GAAS8G,KAAAA,EACTqR,GAAarR,KAAAA,CAAS,CAEzB,CACF,OC9EYsZ,GACXlrB,YACW0F,EACQue,GADRxmB,KAASiI,UAATA,EACQjI,KAAQwmB,SAARA,CACf,CAEJ/L,eACE,OAAOza,KAAKiI,UAAUwS,SAAS5C,IAC7B+J,GAAO,IAAI8L,EAAgB9L,EAAK5hB,KAAKwmB,QAAQ,CAAC,CAEjD,CACD9L,YACE,OAAO1a,KAAKiI,UAAUyS,MAAM7C,IAC1B+J,GAAO,IAAI8L,EAAgB9L,EAAK5hB,KAAKwmB,QAAQ,CAAC,CAEjD,CACD7L,oBACE,OAAO3a,KAAKiI,UAAU0S,eAAiB,IACxC,CACF,OCKY+S,EAGXnrB,YACW0F,EACFkb,GADEnjB,KAASiI,UAATA,EACFjI,KAAOmjB,QAAPA,CACL,CAEJ3gB,WACE,OAAOxC,KAAKiI,UAAUzF,IACvB,CAEDyI,aACE,OAAOjL,KAAKiI,UAAUgD,MACvB,CAEDuO,eACE,OAAOxZ,KAAKiI,UAAUuR,QACvB,CAED8D,WACE,OAAOtd,KAAKiI,UAAUqV,UACvB,CAODqQ,MAAMzF,GACJ,IAAMrN,ENkPD+S,GMlPuB5tB,KAAKiI,UAAWigB,CAAS,EACrD,OAAO,IAAIwF,EAAgB7S,EAAW7a,KAAKmjB,OAAO,CACnD,CAEDuD,WACE,OAAO,IAAIgH,EAAgB1tB,KAAKiI,UAAUye,KAAM1mB,KAAKmjB,OAAO,CAC7D,CAMDwD,aACE,IAAM9L,EAAY7a,KAAKiI,UAAU0e,OACjC,OAAiB,MAAb9L,EACK,KAEF,IAAI6S,EAAgB7S,EAAW7a,KAAKmjB,OAAO,CACnD,CAQD0K,IACEpmB,EACAmR,GAGA,OADA5Y,KAAK6mB,aAAa,KAAK,EAChB,IAAIyG,GACTd,GAAqBxsB,KAAKiI,UAAWR,EAAMmR,CAA0B,EACrE5Y,IAAI,CAEP,CASD8tB,UACEhmB,EACA8C,EAAuB4J,EAAaC,IACpCmE,GAEA5Y,KAAK6mB,aAAa,WAAW,EAC7B,IAAMpf,EAAOsmB,GAAgBnjB,EAAQ9C,CAAK,EACpCmV,EAAapZ,OAAAqZ,OAAA,GAAQtE,CAAQ,EAInC,OAHoC,MAAhCqE,EAA2B,aAAiC,MAApBxV,EAAKqN,cAC/CmI,EAA2B,YAAIxV,EAAKqN,aAE/B,IAAIwY,GACT,IAAIU,GACFhuB,KAAKiI,UACL,IAAIgmB,EAASxmB,EAAKA,KAAM,CAAA,CAAI,EAC5BwV,CAAuD,EAEzDjd,IAAI,CAEP,CAmBD8mB,UACE,ONmGKoH,GADDlmB,EMlGWhI,KAAKiI,SNkGM,CACW,EMnGNmJ,KAC7B+c,GAAK,IAAIV,GAAiBU,EAAGnuB,KAAKmjB,OAAO,CAAC,CAE7C,CAqBDkE,KAAKC,GACH,ON8CF1F,EM9Cc5hB,KAAKiI,UN+CnBqf,EM/C8BA,GAAWnT,KAAAA,ENkDlCia,GADPxM,EAAM5Z,EAAmB4Z,CAAG,EACU0F,CAAO,EMlDOlW,KAChD+c,GAAK,IAAIV,GAAiBU,EAAGnuB,KAAKmjB,OAAO,CAAC,EN4ChC,IACdvB,CM3CC,CAOD/E,cACE,OAAOA,GAAY7c,KAAKiI,SAAS,CAClC,CAWD0f,eACE/O,GAEA,ONTKyV,GADDrmB,EMWFhI,KAAKiI,SNXmB,EMYxB2Q,CNTmC,CMWtC,CAMDgP,iBACE,ONiDK0G,GADDtmB,EMhDkBhI,KAAKiI,SNgDD,CACkB,CMhD7C,CAMDskB,SAEE,OADAvsB,KAAK6mB,aAAa,QAAQ,ENoDrB0H,GADDvmB,EMlDgBhI,KAAKiI,SNkDC,CACgB,CMlD3C,CAEO4e,aAAarkB,GACnB,GAAsD,KAAjDxC,KAAKiI,UAAyBoc,UAAUnZ,KAC3C,MAAMsjB,GAAsBhsB,CAAI,CAEnC,CACF,OC3MYisB,GAGXlsB,YAAmBioB,EAA2BviB,GAA3BjI,KAAGwqB,IAAHA,EAA2BxqB,KAASiI,UAATA,CAA8B,CAE5E6U,4BACE,OAAO9c,KAAKiI,UAAU6U,qBACvB,CAEDY,yBACE,OAAO1d,KAAKiI,UAAUyV,kBACvB,CAMDkE,IAAI1W,GACF,GAAIwjB,GAAMxjB,CAAI,EACZ,MAAMyjB,EACJ,oEAAoE,EAGxE,OAAO,IAAIjB,EAAgB9L,GAAI5hB,KAAKiI,UAAWiD,CAAI,EAAGlL,IAAI,CAC3D,CAMD4uB,WAAW7iB,GACT,GAAI,CAAC2iB,GAAM3iB,CAAG,EACZ,MAAM4iB,EACJ,2EAA2E,EAG/E,IACEE,EAAUjjB,YAAYG,EAAM/L,KAAKiI,UAAmCnF,IAAI,CAKzE,CAJC,MAAO+I,GACP,MAAM8iB,EACJ,gEAAgE,CAEnE,CACD,OAAO,IAAIjB,EAAgB9L,GAAI5hB,KAAKiI,UAAW8D,CAAG,EAAG/L,IAAI,CAC1D,CAED8uB,sBAAsB1D,GACpBprB,KAAKiI,UAAUyV,mBAAqB0N,CACrC,CAED2D,yBAAyB3D,GACvBprB,KAAKiI,UAAU6U,sBAAwBsO,CACxC,CAED4D,YACElsB,EACAkmB,EACA1B,EAEI,IPqQF,IAIJA,EOvQEyB,CPoQF5F,EACArgB,EACAkmB,EACA1B,EAEI,IOzQFyB,CAAuB/oB,KAAKiI,UAAWnF,EAAMkmB,EAAM1B,GP2QrD2H,GAAwB9L,EAAgCrgB,EAAMkmB,EAAM1B,CAAO,CO1Q1E,CACF,CAED,SAASoH,GAAMxjB,GACb,MAAO,kBAAkB0d,KAAK1d,CAAc,CAC9C,ChC3DA,SAASyhB,GACPC,EACA,CAAEC,mBAAoB9gB,CAAG,GAGzB,IAAMye,EAAMoC,EAAUE,YAAY,YAAY,EAAEvB,aAAY,EACtD2D,EAAatC,EAChBE,YAAY,SAAS,EACrBvB,aAAa,CAAE4D,WAAYpjB,CAAG,CAAE,EAMnC,OAJmD,IAAI0iB,GACrDjE,EACA0E,CAAU,CAGd,CAEgCvlB,EAkBhBygB,UAjBRxgB,EAAmB,WAEvB0V,YACAF,GACA5K,aAAAA,EACA4a,QAASX,GACTlI,UAAWmH,GAEb/jB,EAAS0lB,SAASC,kBAChB,IAAIpnB,EA7Ba,iBA6BWykB,GAA8B,QAAA,EACvD/jB,gBAAgBgB,CAAgB,EAChCjB,qBAAqB,CAAA,CAAI,CAAC,EAG/BgB,EAASwjB,mDAA6B"}