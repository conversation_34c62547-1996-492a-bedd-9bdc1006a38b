# AgneX One - MVP v1.0 Foundation

## Version Overview
**Timeline**: 4 weeks  
**Focus**: Core authentication, basic role management, and simple dashboards  
**Goal**: Prove concept and establish foundation for role-based access system

## Core Features

### 🔐 Authentication System
- Firebase Auth integration
- Email/password authentication
- User registration with email verification
- Password reset functionality
- Session management

### 👥 Role Management (3 Roles Only)
- **Admin**: Full system access and user management
- **Client**: View-only access to assigned data
- **Support**: Basic ticket management

### 🏠 Basic Dashboards
- Role-specific landing pages
- Simple navigation based on user role
- Basic user profile management
- System status indicators

### 🎫 Simple Ticket System
- Create new tickets (Support and Client roles)
- View ticket list with basic filtering
- Simple status tracking (Open, In Progress, Closed)
- Basic ticket details view

### ⚙️ User Management (Admin Only)
- Add new users
- Assign roles to users
- View user list
- Basic user profile editing

## Technical Implementation

### Frontend Structure
```
src/
├── app/
│   ├── (auth)/
│   │   ├── login/page.tsx
│   │   ├── register/page.tsx
│   │   └── layout.tsx
│   ├── (dashboard)/
│   │   ├── admin/page.tsx
│   │   ├── client/page.tsx
│   │   ├── support/page.tsx
│   │   └── layout.tsx
│   ├── tickets/
│   │   ├── page.tsx
│   │   ├── new/page.tsx
│   │   └── [id]/page.tsx
│   └── users/
│       └── page.tsx (admin only)
├── components/
│   ├── ui/ (shadcn components)
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   ├── RegisterForm.tsx
│   │   └── AuthGuard.tsx
│   ├── dashboard/
│   │   ├── AdminDashboard.tsx
│   │   ├── ClientDashboard.tsx
│   │   └── SupportDashboard.tsx
│   ├── tickets/
│   │   ├── TicketList.tsx
│   │   ├── TicketForm.tsx
│   │   └── TicketCard.tsx
│   └── users/
│       ├── UserList.tsx
│       └── UserForm.tsx
├── lib/
│   ├── firebase.ts
│   ├── auth.ts
│   └── utils.ts
└── hooks/
    ├── useAuth.ts
    └── useRole.ts
```

### Database Schema (Firestore) - Security-Optimized

#### Users Collection
```javascript
users/{userId} {
  // Basic Info
  email: string,
  displayName: string,
  role: 'admin' | 'client' | 'support',

  // Security & Access Control
  isActive: boolean,
  permissions: {
    canManageUsers: boolean,
    canViewAllTickets: boolean,
    canCreateTickets: boolean,
    canAssignTickets: boolean
  },

  // Client-specific access (for client role)
  clientAccess: {
    clientId: string, // their own client ID
    projectIds: [string], // projects they can access
    canViewFinancials: boolean
  },

  // Audit Fields
  createdAt: timestamp,
  createdBy: string, // admin who created this user
  lastLogin: timestamp,
  updatedAt: timestamp,
  updatedBy: string
}
```

#### Tickets Collection
```javascript
tickets/{ticketId} {
  // Basic Ticket Info
  title: string,
  description: string,
  status: 'open' | 'in_progress' | 'closed',
  priority: 'low' | 'medium' | 'high',

  // Access Control Fields
  createdBy: string, // userId who created
  assignedTo: string, // userId assigned (optional)
  clientId: string, // client this ticket belongs to
  isInternal: boolean, // internal vs client-facing ticket

  // Role-based Access Arrays
  viewableBy: [string], // array of userIds who can view
  editableBy: [string], // array of userIds who can edit

  // Department/Team Access
  department: string, // 'support', 'technical', 'billing'
  teamAccess: [string], // team IDs that can access

  // Audit Fields
  createdAt: timestamp,
  updatedAt: timestamp,
  updatedBy: string, // last person to update

  // Security Metadata
  accessLevel: 'public' | 'internal' | 'restricted',
  dataClassification: 'general' | 'confidential' | 'sensitive'
}
```

#### User Roles Collection (for role management)
```javascript
userRoles/{userId} {
  userId: string,
  role: string,
  permissions: [string], // array of permission strings
  assignedBy: string, // admin who assigned role
  assignedAt: timestamp,
  expiresAt: timestamp, // optional role expiration
  isActive: boolean
}
```

### Custom Claims Structure (Security-Enhanced)
```javascript
{
  // Primary Role
  role: 'admin' | 'client' | 'support',

  // Granular Permissions
  permissions: {
    canManageUsers: boolean,
    canViewAllTickets: boolean,
    canCreateTickets: boolean,
    canAssignTickets: boolean,
    canViewReports: boolean,
    canExportData: boolean
  },

  // Access Scope (for data filtering)
  accessScope: {
    clientId: string, // for client users - their client ID
    departmentIds: [string], // departments they can access
    projectIds: [string], // projects they can access
    teamIds: [string] // teams they belong to
  },

  // Security Metadata
  securityLevel: number, // 1-5 security clearance level
  dataAccess: [string], // types of data they can access
  ipRestrictions: [string], // allowed IP ranges (optional)

  // Audit Info
  lastUpdated: timestamp,
  updatedBy: string // admin who last updated claims
}
```

### Cloud Functions (Basic)
```javascript
// functions/src/index.ts
exports.setUserRole = functions.https.onCall(async (data, context) => {
  // Set custom claims for user role
});

exports.createTicket = functions.firestore
  .document('tickets/{ticketId}')
  .onCreate(async (snap, context) => {
    // Send notification on ticket creation
  });
```

## Development Phases

### Week 1: Project Setup & Authentication
- [ ] Initialize Next.js project with TypeScript
- [ ] Setup Shadcn/ui and Tailwind CSS
- [ ] Configure Firebase project
- [ ] Implement authentication pages (login/register)
- [ ] Setup Firebase Auth with email/password
- [ ] Create basic layout and navigation

### Week 2: Role System & Dashboards
- [ ] Implement custom claims system
- [ ] Create role-based route protection
- [ ] Build basic dashboard layouts for each role
- [ ] Implement user context and role hooks
- [ ] Create navigation based on user roles
- [ ] Setup basic user profile management

### Week 3: Ticket System
- [ ] Design ticket data structure
- [ ] Build ticket creation form
- [ ] Implement ticket listing with basic filtering
- [ ] Create ticket detail view
- [ ] Add basic status management
- [ ] Implement role-based ticket access

### Week 4: User Management & Polish
- [ ] Build admin user management interface
- [ ] Implement user creation and role assignment
- [ ] Add basic error handling and loading states
- [ ] Implement responsive design
- [ ] Add basic notifications/toasts
- [ ] Testing and bug fixes

## UI Components (Shadcn/ui)

### Required Components
- Button, Input, Label, Card
- Form, Select, Textarea
- Table, Badge, Avatar
- Dialog, Alert, Toast
- Tabs, Separator

### Custom Components
- AuthGuard (route protection)
- RoleBasedComponent (conditional rendering)
- TicketStatusBadge
- UserRoleBadge
- DashboardCard

## Security Implementation

### Route Protection
```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  // Check authentication and role-based access
}

// components/auth/AuthGuard.tsx
export function AuthGuard({ children, requiredRole }) {
  // Protect components based on user role
}
```

### Firebase Security Rules (Comprehensive)
```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions for role-based access
    function isAuthenticated() {
      return request.auth != null;
    }

    function getUserRole() {
      return request.auth.token.role;
    }

    function hasPermission(permission) {
      return request.auth.token.permissions[permission] == true;
    }

    function isOwner(ownerId) {
      return request.auth.uid == ownerId;
    }

    function canAccessClient(clientId) {
      return getUserRole() == 'admin' ||
             request.auth.token.accessScope.clientId == clientId;
    }

    // Users Collection Rules
    match /users/{userId} {
      allow read: if isAuthenticated() &&
        (isOwner(userId) ||
         getUserRole() == 'admin' ||
         hasPermission('canManageUsers'));

      allow create: if isAuthenticated() &&
        getUserRole() == 'admin';

      allow update: if isAuthenticated() &&
        (isOwner(userId) ||
         (getUserRole() == 'admin' && hasPermission('canManageUsers')));

      allow delete: if isAuthenticated() &&
        getUserRole() == 'admin' &&
        hasPermission('canManageUsers');
    }

    // Tickets Collection Rules
    match /tickets/{ticketId} {
      allow read: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         hasPermission('canViewAllTickets') ||
         isOwner(resource.data.createdBy) ||
         request.auth.uid == resource.data.assignedTo ||
         canAccessClient(resource.data.clientId) ||
         request.auth.uid in resource.data.viewableBy);

      allow create: if isAuthenticated() &&
        hasPermission('canCreateTickets') &&
        request.auth.uid == request.resource.data.createdBy &&
        (getUserRole() != 'client' ||
         canAccessClient(request.resource.data.clientId));

      allow update: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         hasPermission('canAssignTickets') ||
         isOwner(resource.data.createdBy) ||
         request.auth.uid == resource.data.assignedTo ||
         request.auth.uid in resource.data.editableBy);

      allow delete: if isAuthenticated() &&
        getUserRole() == 'admin';
    }

    // User Roles Collection Rules
    match /userRoles/{userId} {
      allow read: if isAuthenticated() &&
        (isOwner(userId) || getUserRole() == 'admin');

      allow write: if isAuthenticated() &&
        getUserRole() == 'admin' &&
        hasPermission('canManageUsers');
    }
  }
}
```

## Testing Strategy

### Unit Tests
- Authentication functions
- Role-based access logic
- Form validation
- Utility functions

### Integration Tests
- User registration flow
- Login/logout functionality
- Ticket creation and viewing
- Role-based navigation

### Manual Testing
- Cross-browser compatibility
- Mobile responsiveness
- User experience flows
- Security access controls

## Success Metrics

### Technical Metrics
- [ ] 100% authentication success rate
- [ ] Role-based access working correctly
- [ ] All CRUD operations functional
- [ ] Responsive design on mobile/desktop

### User Experience Metrics
- [ ] Intuitive navigation for all roles
- [ ] Clear feedback for user actions
- [ ] Fast page load times (<3s)
- [ ] Error handling with helpful messages

### Business Metrics
- [ ] Users can successfully create accounts
- [ ] Admins can manage users effectively
- [ ] Tickets can be created and tracked
- [ ] Role separation is clear and functional

## Deployment

### Environment Setup
- **Development**: Local Firebase emulator
- **Staging**: Firebase staging project
- **Production**: Firebase production project

### CI/CD Pipeline
- GitHub Actions for automated testing
- Automated deployment to staging
- Manual approval for production deployment

## Known Limitations (v1.0)

### Features Not Included
- HR and Finance roles (added in v2.0)
- Employee management
- Advanced reporting
- AI integration
- File attachments
- Advanced notifications

### Technical Limitations
- Basic UI styling
- Limited error handling
- No offline support
- Basic search functionality

## Next Steps to v2.0
- Add HR and Finance roles
- Implement employee management
- Enhanced ticket system with assignments
- Improved UI/UX design
- Advanced filtering and search

---

**MVP v1.0 Goal**: Establish a solid foundation with core authentication, basic role management, and simple ticket system that proves the concept and allows for user feedback.
