'use client';

import { useAuth } from './useAuth';
import { UserRole } from '@/lib/auth';

export const useRole = () => {
  const { userData } = useAuth();

  const hasRole = (role: UserRole): boolean => {
    return userData?.role === role;
  };

  const hasPermission = (permission: keyof typeof userData.permissions): boolean => {
    if (!userData?.permissions) return false;
    return userData.permissions[permission] === true;
  };

  const canAccessRoute = (requiredRole?: UserRole, requiredPermission?: string): boolean => {
    if (!userData) return false;

    // Check role requirement
    if (requiredRole && !hasRole(requiredRole)) {
      return false;
    }

    // Check permission requirement
    if (requiredPermission && !hasPermission(requiredPermission as keyof typeof userData.permissions)) {
      return false;
    }

    return true;
  };

  const isAdmin = (): boolean => hasRole('admin');
  const isClient = (): boolean => hasRole('client');
  const isSupport = (): boolean => hasRole('support');

  return {
    role: userData?.role,
    permissions: userData?.permissions,
    hasRole,
    hasPermission,
    canAccessRoute,
    isAdmin,
    isClient,
    isSupport,
  };
};
