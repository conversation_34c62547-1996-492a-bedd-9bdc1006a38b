import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Public routes that don't require authentication
  const publicRoutes = ['/', '/login', '/register'];
  
  // Admin-only routes
  const adminRoutes = ['/users', '/dashboard/admin'];
  
  // Support routes
  const supportRoutes = ['/dashboard/support'];
  
  // Client routes
  const clientRoutes = ['/dashboard/client'];

  // Allow public routes
  if (publicRoutes.includes(pathname)) {
    return NextResponse.next();
  }

  // For now, we'll handle authentication and role checking on the client side
  // In a production app, you'd want to verify Firebase tokens here
  // This middleware serves as a placeholder for future server-side auth checks
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
