import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  sendEmailVerification,
  User
} from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { auth, db } from './firebase';

export type UserRole = 'admin' | 'client' | 'support';

export interface UserData {
  email: string;
  displayName: string;
  role: UserRole;
  isActive: boolean;
  permissions: {
    canManageUsers: boolean;
    canViewAllTickets: boolean;
    canCreateTickets: boolean;
    canAssignTickets: boolean;
  };
  clientAccess?: {
    clientId: string;
    projectIds: string[];
    canViewFinancials: boolean;
  };
  createdAt: Date;
  createdBy: string;
  lastLogin?: Date;
  updatedAt: Date;
  updatedBy: string;
}

export const signIn = async (email: string, password: string) => {
  try {
    const result = await signInWithEmailAndPassword(auth, email, password);
    
    // Update last login
    if (result.user) {
      await setDoc(doc(db, 'users', result.user.uid), {
        lastLogin: new Date(),
        updatedAt: new Date(),
      }, { merge: true });
    }
    
    return result;
  } catch (error) {
    throw error;
  }
};

export const signUp = async (email: string, password: string, displayName: string, role: UserRole = 'client') => {
  try {
    const result = await createUserWithEmailAndPassword(auth, email, password);
    
    if (result.user) {
      // Send email verification
      await sendEmailVerification(result.user);
      
      // Create user document in Firestore
      const userData: UserData = {
        email,
        displayName,
        role,
        isActive: true,
        permissions: getDefaultPermissions(role),
        createdAt: new Date(),
        createdBy: result.user.uid,
        updatedAt: new Date(),
        updatedBy: result.user.uid,
      };
      
      // Add client access for client role
      if (role === 'client') {
        userData.clientAccess = {
          clientId: result.user.uid, // Use user ID as client ID for now
          projectIds: [],
          canViewFinancials: false,
        };
      }
      
      await setDoc(doc(db, 'users', result.user.uid), userData);
    }
    
    return result;
  } catch (error) {
    throw error;
  }
};

export const logout = async () => {
  try {
    await signOut(auth);
  } catch (error) {
    throw error;
  }
};

export const resetPassword = async (email: string) => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    throw error;
  }
};

export const getUserData = async (uid: string): Promise<UserData | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', uid));
    if (userDoc.exists()) {
      return userDoc.data() as UserData;
    }
    return null;
  } catch (error) {
    console.error('Error fetching user data:', error);
    return null;
  }
};

export const getDefaultPermissions = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return {
        canManageUsers: true,
        canViewAllTickets: true,
        canCreateTickets: true,
        canAssignTickets: true,
      };
    case 'support':
      return {
        canManageUsers: false,
        canViewAllTickets: true,
        canCreateTickets: true,
        canAssignTickets: true,
      };
    case 'client':
      return {
        canManageUsers: false,
        canViewAllTickets: false,
        canCreateTickets: true,
        canAssignTickets: false,
      };
    default:
      return {
        canManageUsers: false,
        canViewAllTickets: false,
        canCreateTickets: false,
        canAssignTickets: false,
      };
  }
};
