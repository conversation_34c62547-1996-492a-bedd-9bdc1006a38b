'use client';

import { useRole } from '@/hooks/useRole';
import { UserRole } from '@/lib/auth';

interface RoleBasedComponentProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  requiredPermission?: string;
  fallback?: React.ReactNode;
}

export function RoleBasedComponent({ 
  children, 
  allowedRoles, 
  requiredPermission, 
  fallback = null 
}: RoleBasedComponentProps) {
  const { role, hasPermission } = useRole();

  // Check role requirement
  if (allowedRoles && allowedRoles.length > 0) {
    if (!role || !allowedRoles.includes(role)) {
      return <>{fallback}</>;
    }
  }

  // Check permission requirement
  if (requiredPermission) {
    if (!hasPermission(requiredPermission as any)) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
}

// Convenience components for specific roles
export function AdminOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleBasedComponent allowedRoles={['admin']} fallback={fallback}>
      {children}
    </RoleBasedComponent>
  );
}

export function SupportOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleBasedComponent allowedRoles={['support']} fallback={fallback}>
      {children}
    </RoleBasedComponent>
  );
}

export function ClientOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleBasedComponent allowedRoles={['client']} fallback={fallback}>
      {children}
    </RoleBasedComponent>
  );
}

export function AdminOrSupport({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleBasedComponent allowedRoles={['admin', 'support']} fallback={fallback}>
      {children}
    </RoleBasedComponent>
  );
}
