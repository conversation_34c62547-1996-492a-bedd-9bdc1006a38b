# AgneX One - MVP v1.0

A comprehensive role-based access control system built with Next.js, Firebase, and Shadcn/ui.

## Features

- 🔐 **Authentication System**: Firebase Auth with email/password
- 👥 **Role Management**: Admin, Client, and Support roles
- 🏠 **Role-specific Dashboards**: Customized interfaces for each role
- 🎫 **Ticket System**: Create, view, and manage tickets
- ⚙️ **User Management**: Admin interface for user management
- 🛡️ **Security**: Comprehensive Firebase security rules

## Tech Stack

- **Frontend**: Next.js 15 with TypeScript
- **UI Components**: Shadcn/ui with Tailwind CSS
- **Authentication**: Firebase Auth
- **Database**: Firestore
- **Deployment**: Vercel (recommended)

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Firebase project

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd agnex-one
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Configure your Firebase project in `.env.local`:
```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/
│   ├── (auth)/          # Authentication pages
│   ├── (dashboard)/     # Dashboard pages
│   ├── tickets/         # Ticket management
│   └── users/           # User management
├── components/
│   ├── ui/              # Shadcn/ui components
│   ├── auth/            # Authentication components
│   ├── dashboard/       # Dashboard components
│   ├── tickets/         # Ticket components
│   └── users/           # User management components
├── lib/
│   ├── firebase.ts      # Firebase configuration
│   ├── auth.ts          # Authentication utilities
│   └── utils.ts         # General utilities
└── hooks/
    ├── useAuth.ts       # Authentication hook
    └── useRole.ts       # Role management hook
```

## User Roles

### Admin
- Full system access
- User management
- View all tickets
- Create and assign tickets

### Support
- Basic ticket management
- View assigned tickets
- Create tickets
- No user management access

### Client
- View-only access to assigned data
- Create tickets for their projects
- Limited system access

## Firebase Setup

1. Create a new Firebase project
2. Enable Authentication with Email/Password
3. Create a Firestore database
4. Set up security rules (see `firestore.rules`)
5. Configure custom claims for role-based access

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Adding New Components

Use Shadcn/ui CLI to add new components:
```bash
npx shadcn@latest add [component-name]
```

## Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

© 2024 AgneX Studio. All rights reserved.
