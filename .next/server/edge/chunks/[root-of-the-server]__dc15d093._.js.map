{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Public routes that don't require authentication\n  const publicRoutes = ['/', '/login', '/register'];\n  \n  // Admin-only routes\n  const adminRoutes = ['/users', '/dashboard/admin'];\n  \n  // Support routes\n  const supportRoutes = ['/dashboard/support'];\n  \n  // Client routes\n  const clientRoutes = ['/dashboard/client'];\n\n  // Allow public routes\n  if (publicRoutes.includes(pathname)) {\n    return NextResponse.next();\n  }\n\n  // For now, we'll handle authentication and role checking on the client side\n  // In a production app, you'd want to verify Firebase tokens here\n  // This middleware serves as a placeholder for future server-side auth checks\n  \n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,kDAAkD;IAClD,MAAM,eAAe;QAAC;QAAK;QAAU;KAAY;IAEjD,oBAAoB;IACpB,MAAM,cAAc;QAAC;QAAU;KAAmB;IAElD,iBAAiB;IACjB,MAAM,gBAAgB;QAAC;KAAqB;IAE5C,gBAAgB;IAChB,MAAM,eAAe;QAAC;KAAoB;IAE1C,sBAAsB;IACtB,IAAI,aAAa,QAAQ,CAAC,WAAW;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,4EAA4E;IAC5E,iEAAiE;IACjE,6EAA6E;IAE7E,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}