# AgneX One - MVP v2.0 Enhanced Operations

## Version Overview
**Timeline**: 3 weeks  
**Focus**: Complete ticket management, employee system, and all 5 user roles  
**Goal**: Full operational system with comprehensive business workflows

## New Features (Building on v1.0)

### 👥 Complete Role System (5 Roles)
- **Admin**: Full system access and management
- **Client**: Project and fund viewing capabilities
- **HR**: Employee management and role assignments
- **Finance**: Fund management and basic invoicing
- **Support**: Enhanced ticket management with assignments

### 🎫 Enhanced Ticket Management
- Ticket assignment system
- Priority levels and categories
- Internal vs external ticket types
- Ticket conversation/comments
- File attachment support
- Advanced filtering and search
- Ticket templates for common issues

### 👨‍💼 Employee Management System
- Employee profiles with detailed information
- Department and role management
- Document upload and storage
- Basic leave tracking
- Employee search and filtering
- Organizational hierarchy

### 💰 Basic Financial Management
- Client fund tracking
- Project budget allocation
- Simple invoice generation
- Transaction history
- Fund usage reports

### 📊 Enhanced Dashboards
- Role-specific widgets and metrics
- Real-time data updates
- Interactive charts and graphs
- Quick action buttons
- Recent activity feeds

## Technical Enhancements

### Expanded Database Schema

#### Employees Collection (Security-Enhanced)
```javascript
employees/{employeeId} {
  // Basic Employee Info
  personalInfo: {
    firstName: string,
    lastName: string,
    email: string,
    phone: string,
    address: object,
    dateOfBirth: timestamp,
    emergencyContact: object
  },

  // Employment Details
  employment: {
    employeeId: string,
    department: string,
    position: string,
    startDate: timestamp,
    salary: number, // restricted access
    manager: string, // employeeId
    status: 'active' | 'inactive' | 'terminated'
  },

  // Access Control
  accessControl: {
    createdBy: string, // HR user who created
    managedBy: [string], // managers who can access
    hrAccess: [string], // HR users with access
    viewableBy: [string], // users who can view basic info
    editableBy: [string], // users who can edit
    department: string, // for department-based access
    securityClearance: number // 1-5 clearance level
  },

  // Documents with Access Control
  documents: [{
    name: string,
    type: string,
    url: string,
    uploadedAt: timestamp,
    uploadedBy: string,
    accessLevel: 'public' | 'hr_only' | 'manager_only' | 'restricted',
    viewableBy: [string], // specific users who can view
    confidential: boolean
  }],

  // Leave Records
  leaves: [{
    type: string,
    startDate: timestamp,
    endDate: timestamp,
    status: 'pending' | 'approved' | 'rejected',
    reason: string,
    approvedBy: string,
    requestedAt: timestamp,
    processedAt: timestamp,
    isConfidential: boolean
  }],

  // Audit Fields
  createdAt: timestamp,
  createdBy: string,
  updatedAt: timestamp,
  updatedBy: string,
  lastAccessedAt: timestamp,
  lastAccessedBy: string
}
```

#### Projects Collection (Security-Enhanced)
```javascript
projects/{projectId} {
  // Basic Project Info
  name: string,
  description: string,
  clientId: string, // client who owns this project
  status: 'planning' | 'active' | 'on_hold' | 'completed',

  // Financial Information (restricted access)
  budget: {
    allocated: number,
    spent: number,
    remaining: number,
    currency: string,
    lastUpdated: timestamp,
    updatedBy: string
  },

  // Timeline
  timeline: {
    startDate: timestamp,
    endDate: timestamp,
    milestones: [{
      name: string,
      date: timestamp,
      status: string,
      completedBy: string
    }]
  },

  // Team and Access Control
  team: {
    lead: string, // project lead userId
    members: [string], // team member userIds
    managers: [string], // users with management access
    viewers: [string] // users with view-only access
  },

  // Role-based Access
  accessControl: {
    clientAccess: boolean, // can client view this project
    financeAccess: [string], // finance users with access
    hrAccess: boolean, // can HR view team info
    publicVisibility: boolean, // visible to all authenticated users
    confidentialityLevel: 'public' | 'internal' | 'confidential' | 'restricted'
  },

  // Audit Fields
  createdAt: timestamp,
  createdBy: string, // finance/admin user who created
  updatedAt: timestamp,
  updatedBy: string,
  archivedAt: timestamp, // when project was archived
  archivedBy: string
}
```

#### Funds Collection (Security-Enhanced)
```javascript
funds/{fundId} {
  // Basic Fund Info
  clientId: string, // client who owns these funds
  currency: string,

  // Financial Data (highly restricted)
  amounts: {
    total: number,
    allocated: number,
    remaining: number,
    reserved: number, // funds on hold
    lastCalculated: timestamp
  },

  // Access Control (Finance-only data)
  accessControl: {
    financeUsers: [string], // finance users with access
    clientAccess: boolean, // can client view balance
    adminAccess: [string], // admin users with access
    auditAccess: [string], // users for audit purposes
    confidentialityLevel: 'restricted' // always restricted for financial data
  },

  // Transaction History
  transactions: [{
    id: string,
    type: 'deposit' | 'allocation' | 'expense' | 'refund',
    amount: number,
    description: string,
    projectId: string, // optional
    date: timestamp,
    processedBy: string, // finance user who processed
    approvedBy: string, // manager who approved
    status: 'pending' | 'approved' | 'completed' | 'rejected',
    reference: string, // external reference number
    category: string,
    isConfidential: boolean
  }],

  // Approval Workflow
  approvals: {
    required: boolean,
    threshold: number, // amount requiring approval
    approvers: [string], // users who can approve
    currentApprovals: [{
      userId: string,
      approvedAt: timestamp,
      amount: number
    }]
  },

  // Audit Fields
  createdAt: timestamp,
  createdBy: string, // finance user who created
  updatedAt: timestamp,
  updatedBy: string,
  lastAuditAt: timestamp,
  lastAuditBy: string
}
```

#### Enhanced Tickets Collection
```javascript
tickets/{ticketId} {
  // Previous fields from v1.0 plus:
  category: string,
  type: 'internal' | 'external',
  assignedTo: string,
  assignedBy: string,
  assignedAt: timestamp,
  estimatedHours: number,
  actualHours: number,
  tags: [string],
  attachments: [{
    name: string,
    url: string,
    size: number,
    uploadedAt: timestamp
  }],
  comments: [{
    message: string,
    author: string,
    createdAt: timestamp,
    isInternal: boolean
  }],
  resolution: {
    summary: string,
    resolvedBy: string,
    resolvedAt: timestamp
  }
}
```

### Enhanced Cloud Functions

```javascript
// Ticket Management
exports.assignTicket = functions.https.onCall(async (data, context) => {
  // Assign ticket to support agent
});

exports.updateTicketStatus = functions.firestore
  .document('tickets/{ticketId}')
  .onUpdate(async (change, context) => {
    // Send notifications on status changes
  });

// Employee Management
exports.createEmployee = functions.https.onCall(async (data, context) => {
  // Create employee profile with validation
});

exports.uploadEmployeeDocument = functions.storage
  .object()
  .onFinalize(async (object) => {
    // Process uploaded employee documents
  });

// Financial Management
exports.allocateFunds = functions.https.onCall(async (data, context) => {
  // Allocate funds to projects with validation
});

exports.generateInvoice = functions.https.onCall(async (data, context) => {
  // Generate basic PDF invoices
});
```

## Development Phases

### Week 1: Role Expansion & Employee System
- [ ] Add HR and Finance roles to authentication system
- [ ] Update custom claims and permissions
- [ ] Build employee management interface
- [ ] Implement employee CRUD operations
- [ ] Add document upload functionality
- [ ] Create department and role management

### Week 2: Enhanced Ticket System
- [ ] Implement ticket assignment system
- [ ] Add ticket categories and types
- [ ] Build comment/conversation system
- [ ] Add file attachment support
- [ ] Implement advanced filtering and search
- [ ] Create ticket templates

### Week 3: Financial System & Dashboard Enhancement
- [ ] Build fund management interface
- [ ] Implement project budget tracking
- [ ] Add basic invoice generation
- [ ] Create transaction history views
- [ ] Enhance all role dashboards with new data
- [ ] Add real-time updates and notifications

## New UI Components

### Employee Management
- EmployeeList with advanced filtering
- EmployeeForm with multi-step wizard
- DocumentUpload with drag-and-drop
- LeaveTracker with calendar view
- OrganizationChart component

### Enhanced Tickets
- TicketAssignment dropdown
- CommentThread component
- FileAttachment with preview
- TicketFilters with multiple criteria
- TicketTemplates selector

### Financial Management
- FundAllocation interface
- BudgetTracker with progress bars
- InvoiceGenerator form
- TransactionHistory table
- FinancialSummary cards

### Dashboard Widgets
- MetricsCard with trend indicators
- ActivityFeed with real-time updates
- QuickActions button group
- ChartWidget with multiple chart types
- NotificationCenter

## Role-Specific Features

### Admin Enhancements
- Complete user and employee management
- System-wide analytics and reports
- Global settings and configurations
- Audit logs and activity tracking

### Client Features
- Project portfolio view
- Fund balance and transaction history
- Document access and downloads
- Communication with support team

### HR Features
- Employee lifecycle management
- Leave approval workflows
- Document management system
- Organizational structure management

### Finance Features
- Fund allocation and tracking
- Budget management and reporting
- Invoice generation and tracking
- Financial analytics and insights

### Support Enhancements
- Advanced ticket management
- Team collaboration tools
- Knowledge base integration
- Performance metrics tracking

## Security Enhancements

### Enhanced Firebase Rules (Comprehensive Security)
```javascript
// Enhanced security rules for new collections
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function getUserRole() {
      return request.auth.token.role;
    }

    function hasPermission(permission) {
      return request.auth.token.permissions[permission] == true;
    }

    function canAccessClient(clientId) {
      return getUserRole() == 'admin' ||
             request.auth.token.accessScope.clientId == clientId;
    }

    function isInAccessList(accessList) {
      return request.auth.uid in accessList;
    }

    function hasSecurityClearance(requiredLevel) {
      return request.auth.token.securityLevel >= requiredLevel;
    }

    // Employees Collection Rules
    match /employees/{employeeId} {
      allow read: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         (getUserRole() == 'hr' && hasPermission('canManageEmployees')) ||
         request.auth.uid == employeeId ||
         isInAccessList(resource.data.accessControl.viewableBy) ||
         isInAccessList(resource.data.accessControl.managedBy));

      allow create: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         (getUserRole() == 'hr' && hasPermission('canManageEmployees'))) &&
        request.auth.uid == request.resource.data.accessControl.createdBy;

      allow update: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         (getUserRole() == 'hr' && hasPermission('canManageEmployees')) ||
         isInAccessList(resource.data.accessControl.editableBy));

      allow delete: if isAuthenticated() &&
        getUserRole() == 'admin';
    }

    // Projects Collection Rules
    match /projects/{projectId} {
      allow read: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         (getUserRole() == 'finance' && hasPermission('canViewProjects')) ||
         canAccessClient(resource.data.clientId) ||
         isInAccessList(resource.data.team.members) ||
         isInAccessList(resource.data.team.viewers) ||
         resource.data.accessControl.publicVisibility == true);

      allow create: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         (getUserRole() == 'finance' && hasPermission('canCreateProjects'))) &&
        request.auth.uid == request.resource.data.createdBy;

      allow update: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         (getUserRole() == 'finance' && hasPermission('canEditProjects')) ||
         isInAccessList(resource.data.team.managers));

      allow delete: if isAuthenticated() &&
        getUserRole() == 'admin';
    }

    // Funds Collection Rules (Highly Restricted)
    match /funds/{fundId} {
      allow read: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         (getUserRole() == 'finance' && hasPermission('canViewFunds')) ||
         (canAccessClient(resource.data.clientId) &&
          resource.data.accessControl.clientAccess == true) ||
         isInAccessList(resource.data.accessControl.financeUsers));

      allow create: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         (getUserRole() == 'finance' && hasPermission('canManageFunds'))) &&
        request.auth.uid == request.resource.data.createdBy &&
        hasSecurityClearance(3); // Require high security clearance

      allow update: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         (getUserRole() == 'finance' && hasPermission('canManageFunds'))) &&
        hasSecurityClearance(3);

      allow delete: if isAuthenticated() &&
        getUserRole() == 'admin' &&
        hasSecurityClearance(4); // Highest security for deletion
    }

    // Enhanced Tickets Rules (from v1.0)
    match /tickets/{ticketId} {
      allow read: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         hasPermission('canViewAllTickets') ||
         request.auth.uid == resource.data.createdBy ||
         request.auth.uid == resource.data.assignedTo ||
         canAccessClient(resource.data.clientId) ||
         isInAccessList(resource.data.viewableBy) ||
         (resource.data.isInternal == false &&
          resource.data.accessLevel != 'restricted'));

      allow create: if isAuthenticated() &&
        hasPermission('canCreateTickets') &&
        request.auth.uid == request.resource.data.createdBy;

      allow update: if isAuthenticated() &&
        (getUserRole() == 'admin' ||
         hasPermission('canAssignTickets') ||
         request.auth.uid == resource.data.createdBy ||
         request.auth.uid == resource.data.assignedTo ||
         isInAccessList(resource.data.editableBy));
    }
  }
}
```

## Performance Optimizations

### Data Loading
- Implement pagination for large datasets
- Add infinite scrolling for ticket lists
- Use React Query for caching and synchronization
- Optimize Firestore queries with proper indexing

### UI Performance
- Implement lazy loading for components
- Add skeleton loading states
- Optimize image loading and caching
- Use React.memo for expensive components

## Testing Strategy

### Automated Testing
- Unit tests for all new business logic
- Integration tests for role-based workflows
- E2E tests for critical user journeys
- Performance testing for data-heavy operations

### User Acceptance Testing
- Role-specific workflow validation
- Cross-role interaction testing
- Mobile responsiveness verification
- Accessibility compliance testing

## Success Metrics

### Functional Metrics
- [ ] All 5 roles implemented and functional
- [ ] Complete ticket lifecycle working
- [ ] Employee management fully operational
- [ ] Financial tracking accurate and reliable

### Performance Metrics
- [ ] Page load times under 2 seconds
- [ ] Real-time updates working smoothly
- [ ] File uploads completing successfully
- [ ] Search and filtering responsive

### User Experience Metrics
- [ ] Intuitive navigation for all roles
- [ ] Clear data visualization
- [ ] Efficient workflow completion
- [ ] Positive user feedback on usability

## Known Limitations (v2.0)

### Features for v3.0
- AI integration and automation
- Advanced reporting and analytics
- Multi-language support
- Advanced notification system

### Technical Debt
- Basic error handling (to be enhanced)
- Limited offline capabilities
- Basic search functionality
- Simple notification system

## Migration from v1.0

### Database Migration
- Add new collections (employees, projects, funds)
- Update existing user documents with new fields
- Migrate ticket data to enhanced schema
- Update security rules

### User Migration
- Update custom claims for existing users
- Assign appropriate roles to current users
- Migrate existing tickets to new format
- Preserve user preferences and settings

---

**MVP v2.0 Goal**: Deliver a complete operational system with all business workflows functional, setting the stage for AI integration in v3.0.
